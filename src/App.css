*{padding: 0; margin: 0; box-sizing: border-box;}
#root{flex: 1;}

form .error{
    color: red;
    margin-top: 8px;
}
.sidebar-menu .sidebar-link{
    cursor: pointer;
}
.un-validation{
    color: red !important;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}
.align-left{
 text-align: left;
}

.align-center{
 text-align: center;
}

/* /Start Loader Css/ */
 .preloader {
    background-color: #ffffff00;
    bottom: 0;
    height: 100%;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    z-index: 9999;
}

.preloader .lds-ellipsis {
    margin: 0 auto;
    position: relative;
    top: 50%;
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 64px;
    text-align: center;
    z-index: 9999;
}

.preloader .lds-ellipsis span {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: var(--bs-primary);
    ;
    -webkit-animation: ball-pulse-sync .6s 0s infinite ease-in-out;
    animation: ball-pulse-sync .6s 0s infinite ease-in-out;
}

.preloader .lds-ellipsis span:nth-child(1) {
    -webkit-animation: ball-pulse-sync .6s -.14s infinite ease-in-out;
    animation: ball-pulse-sync .6s -.14s infinite ease-in-out
}

.preloader .lds-ellipsis span:nth-child(2) {
    -webkit-animation: ball-pulse-sync .6s -70ms infinite ease-in-out;
    animation: ball-pulse-sync .6s -70ms infinite ease-in-out
}

@-webkit-keyframes ball-pulse-sync {
    33% {
        -webkit-transform: translateY(10px);
        transform: translateY(10px)
    }

    66% {
        -webkit-transform: translateY(-10px);
        transform: translateY(-10px)
    }

    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@keyframes ball-pulse-sync {
    33% {
        -webkit-transform: translateY(10px);
        transform: translateY(10px)
    }

    66% {
        -webkit-transform: translateY(-10px);
        transform: translateY(-10px)
    }

    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

/* /End Loader Css */