import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const productDataApiSlice = createApi({
  reducerPath: "productDataApiSlice",
  baseQuery,
  tagTypes: [
    "productData",
    "singleProduct",
    "productDetailData",
    "singleProductDetail",
  ],
  endpoints: (builder) => ({
    getProductList: builder.query({
      query: (body) => ({
        url: `products/listProducts`,
        method: "POST",
        body,
      }),
      providesTags: ["productData"],
    }),
    listAllProduct: builder.query({
      query: (body) => ({
        url: `products/listAllProducts`,
        method: "POST",
        body,
      }),
    }),
    deleteProduct: builder.mutation({
      query: (body) => ({
        url: `products/deleteProduct`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["productData"],
    }),
    createProduct: builder.mutation({
      query: (body) => ({
        url: `products/createProduct`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["productData"],
    }),
    editProduct: builder.mutation({
      query: (body) => ({
        url: `products/editProduct`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["productData", "singleProduct"],
    }),
    createUpdateVariationsProducts: builder.mutation({
      query: (body) => ({
        url: `products/createUpdateVariationsProducts`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["productData"],
    }),
    getVariationsProducts: builder.mutation({
      query: (body) => ({
        url: `products/getVariationsProducts`,
        method: "POST",
        body,
      }),
    }),
    singleProduct: builder.query({
      query: (body) => ({
        url: `products/singleProduct`,
        method: "POST",
        body,
      }),
      providesTags: ["singleProduct"],
    }),
    getProductdetailList: builder.query({
      query: (body) => ({
        url: `productsDetails/listProductsDetails`,
        method: "POST",
        body,
      }),
      providesTags: ["productDetailData"],
    }),
    createProductDetail: builder.mutation({
      query: (body) => ({
        url: `productsDetails/createProductDetail`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["productDetailData"],
    }),
    editProductDetail: builder.mutation({
      query: (body) => ({
        url: `productsDetails/editProductsDetails`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["productDetailData", "singleProductDetail"],
    }),
    singleProductDetail: builder.query({
      query: (body) => ({
        url: `productsDetails/singleProductDetail`,
        method: "POST",
        body,
      }),
      providesTags: ["singleProductDetail"],
    }),
    deleteProductDetail: builder.mutation({
      query: (body) => ({
        url: `productsDetails/deleteProductsDetails`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["productDetailData"],
    }),
    getAllProductdetailList: builder.query({
      query: (body) => ({
        url: `productsDetails/listAllProductsDetails`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetProductListQuery,
  useListAllProductQuery,
  useDeleteProductMutation,
  useCreateProductMutation,
  useEditProductMutation,
  useCreateUpdateVariationsProductsMutation,
  useGetVariationsProductsMutation,
  useSingleProductQuery,
  useGetProductdetailListQuery,
  useCreateProductDetailMutation,
  useEditProductDetailMutation,
  useSingleProductDetailQuery,
  useDeleteProductDetailMutation,
  useGetAllProductdetailListQuery
} = productDataApiSlice;
