import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const purchaseInvoiceDataApiSlice = createApi({
  reducerPath: "purchaseInvoiceDataApiSlice",
  baseQuery,
  tagTypes: [
    "purchaseInvoiceData",
    "singlePurchaseInvoice",
    "purchaseInvoiceHistoryData",
  ],
  endpoints: (builder) => ({
    getPurchaseInvoiceTypeList: builder.query({
      query: (body) => ({
        url: `purchaseInvoice/purchaseInvoiceType`,
        method: "GET",
        body,
      }),
    }),
    listPurchaseInvoice: builder.query({
      query: (body) => ({
        url: `purchaseInvoice/listPurchaseInvoice`,
        method: "POST",
        body,
      }),
      providesTags: ["purchaseInvoiceData"],
    }),
    deletePurchaseInvoice: builder.mutation({
      query: (body) => ({
        url: `purchaseInvoice/deletePurchaseInvoice`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["purchaseInvoiceData"],
    }),
    createPurchaseInvoice: builder.mutation({
      query: (body) => ({
        url: `purchaseInvoice/createPurchaseInvoice`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["purchaseInvoiceData"],
    }),
    editPurchaseInvoice: builder.mutation({
      query: (body) => ({
        url: `purchaseInvoice/editPurchaseInvoice`,
        method: "PUT",
        body,
      }),
      invalidatesTags: [
        "purchaseInvoiceData",
        "singlePurchaseInvoice",
        "purchaseInvoiceHistoryData",
      ],
    }),
    getPurchaseInvoiceStatuses: builder.query({
      query: (body) => ({
        url: `purchaseInvoice/purchaseInvoiceStatus`,
        method: "GET",
        body,
      }),
    }),
    singlePurchaseInvoice: builder.query({
      query: (body) => ({
        url: `purchaseInvoice/getSinglePurchaseInvoiceByInvoiceId`,
        method: "POST",
        body,
      }),
      providesTags: ["singlePurchaseInvoice"],
    }),
    listPurchaseInvoiceHistory: builder.query({
      query: (body) => ({
        url: `purchaseInvoiceHistory/listPurchaseInvoiceHistory`,
        method: "POST",
        body,
      }),
      providesTags: ["purchaseInvoiceHistoryData"],
    }),
  }),
});

export const {
  useGetPurchaseInvoiceTypeListQuery,
  useLazyGetPurchaseInvoiceTypeListQuery,
  useListPurchaseInvoiceQuery,
  useDeletePurchaseInvoiceMutation,
  useCreatePurchaseInvoiceMutation,
  useEditPurchaseInvoiceMutation,
  useGetPurchaseInvoiceStatusesQuery,
  useLazyGetPurchaseInvoiceStatusesQuery,
  useSinglePurchaseInvoiceQuery,
  useListPurchaseInvoiceHistoryQuery,
} = purchaseInvoiceDataApiSlice;
