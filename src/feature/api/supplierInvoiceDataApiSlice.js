import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const supplierInvoiceDataApiSlice = createApi({
  reducerPath: "supplierInvoiceDataApiSlice",
  baseQuery,
  tagTypes: [
    "supplierInvoiceData",
    "singleSupplierInvoice",
    "supplierInvoiceHistoryData",
  ],
  endpoints: (builder) => ({
    getSupplierInvoiceTypeList: builder.query({
      query: (body) => ({
        url: `supplierInvoice/supplierInvoiceType`,
        method: "GET",
        body,
      }),
    }),
    listSupplierInvoice: builder.query({
      query: (body) => ({
        url: `supplierInvoice/listSupplierInvoice`,
        method: "POST",
        body,
      }),
      providesTags: ["supplierInvoiceData"],
    }),
    deleteSupplierInvoice: builder.mutation({
      query: (body) => ({
        url: `supplierInvoice/deleteSupplierInvoice`,
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["supplierInvoiceData"],
    }),
    createSupplierInvoice: builder.mutation({
      query: (body) => ({
        url: `supplierInvoice/createSupplierInvoice`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["supplierInvoiceData"],
    }),
    editSupplierInvoice: builder.mutation({
      query: (body) => ({
        url: `supplierInvoice/editSupplierInvoice`,
        method: "PUT",
        body,
      }),
      invalidatesTags: [
        "supplierInvoiceData",
        "singleSupplierInvoice",
        "supplierInvoiceHistoryData",
      ],
    }),
    getSupplierInvoiceStatuses: builder.query({
      query: (body) => ({
        url: `supplierInvoice/supplierInvoiceStatus`,
        method: "GET",
        body,
      }),
    }),
    singleSupplierInvoice: builder.query({
      query: (body) => ({
        url: `supplierInvoice/getSingleSupplierInvoiceByInvoiceId`,
        method: "POST",
        body,
      }),
      providesTags: ["singleSupplierInvoice"],
    }),
    listSupplierInvoiceHistory: builder.query({
      query: (body) => ({
        url: `supplierInvoiceHistory/listSupplierInvoiceHistory`,
        method: "POST",
        body,
      }),
      providesTags: ["supplierInvoiceHistoryData"],
    }),
    listAllSupplierInvoice: builder.query({
      query: (body) => ({
        url: `supplierInvoice/listAllSupplierInvoice`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  useGetSupplierInvoiceTypeListQuery,
  useLazyGetSupplierInvoiceTypeListQuery,
  useListSupplierInvoiceQuery,
  useDeleteSupplierInvoiceMutation,
  useCreateSupplierInvoiceMutation,
  useEditSupplierInvoiceMutation,
  useGetSupplierInvoiceStatusesQuery,
  useLazyGetSupplierInvoiceStatusesQuery,
  useSingleSupplierInvoiceQuery,
  useListSupplierInvoiceHistoryQuery,
  useListAllSupplierInvoiceQuery,
} = supplierInvoiceDataApiSlice;
