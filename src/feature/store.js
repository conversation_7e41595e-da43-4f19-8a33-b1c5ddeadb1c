import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";

import { persistReducer, persistStore } from "redux-persist";

import storage from "redux-persist/lib/storage";
// api - slice
import { authApiSlice } from "./api/authApiSlice";
import { shopDataApiSlice } from "./api/shopDataApiSlice";
import { branchDataApiSlice } from "./api/branchDataApiSlice";
import { statusApiSlice } from "./api/statusApiSlice";
import { rolesDataApiSlice } from "./api/rolesDataApiSlice";
import { staffsDataApiSlice } from "./api/staffsDataApiSlice";
import { rootReducer } from "./rootReducer";
import { categoriesDataApiSlice } from "./api/categoriesDataApiSlice";
import { subCategoriesDataApiSlice } from "./api/subCategoriesDataApiSlice";
import { childCategoriesDataApiSlice } from "./api/childCategoriesDataApiSlice";
import { attributesDataApiSlice } from "./api/attributesDataApiSlice";
import { attributesValuesDataApiSlice } from "./api/attributesValuesDataApiSlice";
import { brandsDataApiSlice } from "./api/brandsDataApiSlice";
import { productDataApiSlice } from "./api/productDataApiSlice";
import { inventoryDataApiSlice } from "./api/inventoryDataApiSlice";
import { dashboardDataApiSlice } from "./api/dashboardDataApiSlice";
import { customerDataApiSlice } from "./api/customerDataApiSlice";
import { customerContactsDataApiSlice } from "./api/customerContactsDataApiSlice";
import { customerNotesDataApiSlice } from "./api/customerNotesDataApiSlice";
import { customerAttachmentsDataApiSlice } from "./api/customerAttachmentsDataApiSlice";
import { supplierContactsDataApiSlice } from "./api/supplierContactsDataApiSlice";
import { supplierNotesDataApiSlice } from "./api/supplierNotesDataApiSlice";
import { supplierAttachmentsDataApiSlice } from "./api/supplierAttachmentsDataApiSlice";
import { supplierDataApiSlice } from "./api/supplierDataApiSlice";
import { countriesDataApiSlice } from "./api/countriesDataApiSlice";
import { supplierInvoiceDataApiSlice } from "./api/supplierInvoiceDataApiSlice";
import { userProfileDataApiSlice } from "./api/userProfileDataApiSlice";
import { purchaseInvoiceDataApiSlice } from "./api/purchaseInvoiceDataApiSlice";

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["authState", "appConfig", "commonState"],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({ serializableCheck: false }).concat(
      authApiSlice.middleware,
      shopDataApiSlice.middleware,
      branchDataApiSlice.middleware,
      statusApiSlice.middleware,
      rolesDataApiSlice.middleware,
      staffsDataApiSlice.middleware,
      categoriesDataApiSlice.middleware,
      subCategoriesDataApiSlice.middleware,
      childCategoriesDataApiSlice.middleware,
      attributesDataApiSlice.middleware,
      attributesValuesDataApiSlice.middleware,
      brandsDataApiSlice.middleware,
      productDataApiSlice.middleware,
      inventoryDataApiSlice.middleware,
      dashboardDataApiSlice.middleware,
      customerDataApiSlice.middleware,
      customerContactsDataApiSlice.middleware,
      customerNotesDataApiSlice.middleware,
      customerAttachmentsDataApiSlice.middleware,
      supplierDataApiSlice.middleware,
      supplierContactsDataApiSlice.middleware,
      supplierNotesDataApiSlice.middleware,
      supplierAttachmentsDataApiSlice.middleware,
      countriesDataApiSlice.middleware,
      supplierInvoiceDataApiSlice.middleware,
      userProfileDataApiSlice.middleware,
      purchaseInvoiceDataApiSlice.middleware,
    ),
});
export const persistor = persistStore(store);

setupListeners(store.dispatch);
