import { combineReducers } from "@reduxjs/toolkit";

import authState from "./slice/authSlice";
import appConfig from "./slice/appConfigSlice";
import commonState from "./slice/commonSlice";
import { authApiSlice } from "./api/authApiSlice";
import { shopDataApiSlice } from "./api/shopDataApiSlice";
import { branchDataApiSlice } from "./api/branchDataApiSlice";
import { statusApiSlice } from "./api/statusApiSlice";
import { rolesDataApiSlice } from "./api/rolesDataApiSlice";
import { staffsDataApiSlice } from "./api/staffsDataApiSlice";
import { categoriesDataApiSlice } from "./api/categoriesDataApiSlice";
import { subCategoriesDataApiSlice } from "./api/subCategoriesDataApiSlice";
import { childCategoriesDataApiSlice } from "./api/childCategoriesDataApiSlice";
import { attributesDataApiSlice } from "./api/attributesDataApiSlice";
import { attributesValuesDataApiSlice } from "./api/attributesValuesDataApiSlice";
import { brandsDataApiSlice } from "./api/brandsDataApiSlice";
import { productDataApiSlice } from "./api/productDataApiSlice";
import { inventoryDataApiSlice } from "./api/inventoryDataApiSlice";
import { dashboardDataApiSlice } from "./api/dashboardDataApiSlice";
import { customerDataApiSlice } from "./api/customerDataApiSlice";
import { customerContactsDataApiSlice } from "./api/customerContactsDataApiSlice";
import { customerNotesDataApiSlice } from "./api/customerNotesDataApiSlice";
import { customerAttachmentsDataApiSlice } from "./api/customerAttachmentsDataApiSlice";
import { supplierDataApiSlice } from "./api/supplierDataApiSlice";
import { supplierContactsDataApiSlice } from "./api/supplierContactsDataApiSlice";
import { supplierNotesDataApiSlice } from "./api/supplierNotesDataApiSlice";
import { supplierAttachmentsDataApiSlice } from "./api/supplierAttachmentsDataApiSlice";
import { countriesDataApiSlice } from "./api/countriesDataApiSlice";
import { supplierInvoiceDataApiSlice } from "./api/supplierInvoiceDataApiSlice";
import { userProfileDataApiSlice } from "./api/userProfileDataApiSlice";
import { purchaseInvoiceDataApiSlice } from "./api/purchaseInvoiceDataApiSlice";

export const rootReducer = combineReducers({
  authState,
  appConfig,
  commonState,
  [authApiSlice.reducerPath]: authApiSlice.reducer,
  [countriesDataApiSlice.reducerPath]: countriesDataApiSlice.reducer,
  [shopDataApiSlice.reducerPath]: shopDataApiSlice.reducer,
  [branchDataApiSlice.reducerPath]: branchDataApiSlice.reducer,
  [statusApiSlice.reducerPath]: statusApiSlice.reducer,
  [rolesDataApiSlice.reducerPath]: rolesDataApiSlice.reducer,
  [staffsDataApiSlice.reducerPath]: staffsDataApiSlice.reducer,
  [categoriesDataApiSlice.reducerPath]: categoriesDataApiSlice.reducer,
  [subCategoriesDataApiSlice.reducerPath]: subCategoriesDataApiSlice.reducer,
  [childCategoriesDataApiSlice.reducerPath]:
    childCategoriesDataApiSlice.reducer,
  [attributesDataApiSlice.reducerPath]: attributesDataApiSlice.reducer,
  [attributesValuesDataApiSlice.reducerPath]:
    attributesValuesDataApiSlice.reducer,
  [brandsDataApiSlice.reducerPath]: brandsDataApiSlice.reducer,
  [productDataApiSlice.reducerPath]: productDataApiSlice.reducer,
  [inventoryDataApiSlice.reducerPath]: inventoryDataApiSlice.reducer,
  [dashboardDataApiSlice.reducerPath]: dashboardDataApiSlice.reducer,
  [customerDataApiSlice.reducerPath]: customerDataApiSlice.reducer,
  [customerContactsDataApiSlice.reducerPath]:
    customerContactsDataApiSlice.reducer,
  [customerNotesDataApiSlice.reducerPath]: customerNotesDataApiSlice.reducer,
  [customerAttachmentsDataApiSlice.reducerPath]:
    customerAttachmentsDataApiSlice.reducer,
  [supplierDataApiSlice.reducerPath]: supplierDataApiSlice.reducer,
  [supplierContactsDataApiSlice.reducerPath]:
    supplierContactsDataApiSlice.reducer,
  [supplierNotesDataApiSlice.reducerPath]: supplierNotesDataApiSlice.reducer,
  [supplierAttachmentsDataApiSlice.reducerPath]:
    supplierAttachmentsDataApiSlice.reducer,
  [supplierInvoiceDataApiSlice.reducerPath]:
    supplierInvoiceDataApiSlice.reducer,
  [userProfileDataApiSlice.reducerPath]: userProfileDataApiSlice.reducer,
  [purchaseInvoiceDataApiSlice.reducerPath]:
    purchaseInvoiceDataApiSlice.reducer,
});
