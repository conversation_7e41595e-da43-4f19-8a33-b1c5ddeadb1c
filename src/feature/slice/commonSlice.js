import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  countries: [],
};

const commonSlice = createSlice({
  name: "commonState",
  initialState,
  reducers: {
    setCountries(state, action) {
      state.countries = action?.payload || [];
    },
    clearCountries(state) {
      state.countries = [];
    },
    setGeneralStatuses(state, action) {
      state.generalStatus = action?.payload || [];
    },
    clearGeneralStatuses(state) {
      state.generalStatus = [];
    },
    setUserStatuses(state, action) {
      state.userStatus = action?.payload || [];
    },
    clearUserStatuses(state) {
      state.userStatus = [];
    },
    setStaffStatuses(state, action) {
      state.staffStatus = action?.payload || [];
    },
    clearStaffStatuses(state) {
      state.staffStatus = [];
    },
    setShopStatuses(state, action) {
      state.shopStatus = action?.payload || [];
    },
    clearShopStatuses(state) {
      state.shopStatus = [];
    },
    setBranchStatuses(state, action) {
      state.branchStatus = action?.payload || [];
    },
    clearBranchStatuses(state) {
      state.branchStatus = [];
    },
    setBranchTypes(state, action) {
      state.branchTypes = action?.payload || [];
    },
    clearBranchTypes(state) {
      state.branchTypes = [];
    },
    setSupplierInvoiceTypes(state, action) {
      state.supplierInvoiceTypes = action?.payload || [];
    },
    clearSupplierInvoiceTypes(state) {
      state.supplierInvoiceTypes = [];
    },
    setSupplierInvoiceStatuses(state, action) {
      state.supplierInvoiceStatus = action?.payload || [];
    },
    clearSupplierInvoiceStatuses(state) {
      state.supplierInvoiceStatus = [];
    },
    setPurchaseInvoiceTypes(state, action) {
      state.purchaseInvoiceTypes = action?.payload || [];
    },
    clearPurchaseInvoiceTypes(state) {
      state.purchaseInvoiceTypes = [];
    },
    setPurchaseInvoiceStatuses(state, action) {
      state.purchaseInvoiceStatus = action?.payload || [];
    },
    clearPurchaseInvoiceStatuses(state) {
      state.purchaseInvoiceStatus = [];
    },
  },
});

export const {
  setCountries,
  clearCountries,
  setGeneralStatuses,
  clearGeneralStatuses,
  setBranchStatuses,
  clearBranchStatuses,
  setUserStatuses,
  clearUserStatuses,
  setShopStatuses,
  clearShopStatuses,
  setStaffStatuses,
  clearStaffStatuses,
  setBranchTypes,
  clearBranchTypes,
  setSupplierInvoiceTypes,
  clearSupplierInvoiceTypes,
  setSupplierInvoiceStatuses,
  clearSupplierInvoiceStatuses,
  setPurchaseInvoiceTypes,
  clearPurchaseInvoiceTypes,
  setPurchaseInvoiceStatuses,
  clearPurchaseInvoiceStatuses,
} = commonSlice.actions;
export default commonSlice.reducer;
