import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import {
  useCreateAttributesMutation,
  useDeleteAttributesMutation,
  useEditAttributesMutation,
  useGetAttributesQuery,
} from "../../feature/api/attributesDataApiSlice";
import { useMemo, useState } from "react";
import { PaginationComponent } from "../../components/pagination";
import { Table } from "../../components/datatable";
import { Modal } from "react-bootstrap";
import { Form } from "react-router-dom";
import { Formik } from "formik";
import FormikField from "../../components/formikField";
import * as yup from "yup";
import WebLoader from "../../components/webLoader";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";
// import useConfirm from "../../hooks/useConfirm";
import { useSelector } from "react-redux";
import useConfirm from "../../hooks/useConfirm";

const initialValues = {
  title: "",
  title_ar: "",
  is_publish: "",
};

const validation = yup.object().shape({
  title: yup.string().required().label("Attribute Name in English"),
  title_ar: yup.string().required().label("Attribute Name in Arabic"),
  is_publish: yup.string().required().label("Attribute Status"),
});
export default function Attributes() {
  const activePage = "Attributes";
  const linkHref = "/dashboard";

  /* **************** Data Add Model ******************* */
  const [showAddModel, setShowAddModel] = useState(false);
  const handleAddModelClose = () => setShowAddModel(false);
  const handleAddModelShow = () => setShowAddModel(true);
  /* **************** End Data Add Model ******************* */

  /* **************** Data Edit Model ******************* */
  const [showEditModel, setShowEditModel] = useState(false);
  const handleEditModelClose = () => setShowEditModel(false);
  const handleEditModelShow = () => setShowEditModel(true);
  /* **************** End Data Edit Model ******************* */
  /* **************** Start list all Attributes ******************* */
  const [currentPage, setCurrentPage] = useState(1);

  const [filterStatus, setFilterStatus] = useState("");
  const [filterKeywords, setFilterKeywords] = useState("");
  const { data: attributesData, isLoading } = useGetAttributesQuery({
    page: currentPage,
    status: parseInt(filterStatus),
    keywords: filterKeywords,
  });
  const attributesList = useMemo(() => {
    if (!attributesData?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return attributesData?.data?.list;
  }, [currentPage, attributesData?.data.list]);
  const pageData = useMemo(
    () => attributesData?.data?.page ?? null,
    [attributesData]
  );
  /* **************** End list all Attributes ******************* */

  /* ****************  Start Filter ****************** */
  const handleStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** Start Delete Sub Attributes ******************* */
  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector((state) => state.commonState.generalStatus);
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** Start list General Status ******************* */
  /* **************** Start Create Attributes ******************* */
  const [handleCreateDataApi, { isLoading: isCreateLoading }] =
    useCreateAttributesMutation();
  const handleCreateFormSubmitFunction = async (body) => {
    try {
      const updatedBody = {
        ...body,
        is_publish: parseInt(body.is_publish),
      };
      const resp = await handleCreateDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleAddModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create Attributes ******************* */
  /* **************** Start Delete Attributes ******************* */

const { showSimpleConfirm } = useConfirm({
  title: 'Confirm Delete Attribute',
  text: 'Are you sure you want to delete this attribute?',
  confirmButtonText: 'Yes, delete attribute!',
  cancelButtonText: 'Cancel'
});

const [handleDeleteDataApi, { isLoading: isDeleteLoading }] =
  useDeleteAttributesMutation();
const onDeleteDataHandler = async (id) => {
  const confirmed = await showSimpleConfirm();
  if (confirmed) {
    try {
      const body = { id: id };
      const resp = await handleDeleteDataApi(body).unwrap();
      handleApiSuccess(resp);
    } catch (error) {
      handleApiErrors(error);
    }
  }
};
  /* **************** End Delete Attributes ******************* */
  /* **************** Start Edit Attributes ******************* */
  const [editValues, setEditValues] = useState({
    id: "",
    title: "",
    title_ar: "",
    is_publish: "",
  });
  const handleOpenModal = (values) => {
    setEditValues(values);
    handleEditModelShow();
  };

  const onEditDataDetailsHandler = (d) => {
    handleOpenModal({
      id: d?.id || "",
      title: d?.title || "",
      title_ar: d?.title_ar || "",
      is_publish: d?.is_publish || "",
    });
  };
  const [handleEditDataApi, { isLoading: isEditLoading }] =
    useEditAttributesMutation();
  const DataUpdateFormSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        id: parseInt(body.id),
        is_publish: parseInt(body.is_publish),
      };
      const resp = await handleEditDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleEditModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Attributes ******************* */
  /* **************** Web Loader  ******************* */
  if (isLoading || isCreateLoading || isDeleteLoading || isEditLoading)
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Attributes List</h4>                  
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <button
                          type="button"
                          className="btn btn-primary"
                          onClick={handleAddModelShow}
                        >
                          Create Attribute
                        </button>
                      </li>
                    </ol>
                  </nav>
                  
                </div>
                <div className="alert customize-alert alert-dismissible text-primary alert-light-primary bg-primary-subtle fade show remove-close-icon mt-2" role="alert">
                    <span className="side-line bg-primary"></span>
                    <div className="d-flex align-items-center ">
                      <i className="ti ti-info-circle fs-5 text-primary me-2 flex-shrink-0"></i>
                      <span className="">
                        Note: Add product variations like color, size, or material as attributes in this module. Each combination generates a unique SKU for precise inventory, pricing, and tracking.
                      </span>
                    </div>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                  <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2 "
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {generalStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "title",
                        label: "Attribute Name",
                        align: "left",
                      },
                      {
                        key: "title_ar",
                        label: "Attribute Name in Arabic",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                      {
                        key: "created_at",
                        label: "Created At",
                        align: "left",
                      },
                    ]}
                    data={attributesList}
                    onDeleteHandler={onDeleteDataHandler}
                    onEditHandler={onEditDataDetailsHandler}
                  />
                  <PaginationComponent
                    totalCount={pageData?.total_count}
                    pageSize={pageData?.page_size}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    onPageChange={fetchData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Create Modal --> */}

      <Modal
        show={showAddModel}
        onHide={handleAddModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title>Create Attribute </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={initialValues}
            validationSchema={validation}
            onSubmit={handleCreateFormSubmitFunction}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-create"
                className="needs-validation"
                autoComplete="off"
              >
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title" className="form-label">
                          Attribute Name in English{" "}
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title"
                          id="title"
                          placeholder="Attribute Name in English "
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title_ar" className="form-label">
                          Attribute Name in Arabic{" "}
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title_ar"
                          id="title_ar"
                          placeholder="Attribute Name in Arabic "
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="is_publish" className="form-label">
                          Status
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="is_publish"
                          id="is_publish"
                          className="form-select"
                          type="select"
                          options={generalStatusList}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                    data-bs-dismiss="modal"
                    onClick={handleAddModelClose}
                  >
                    Close
                  </button>
                  <button
                    className="btn btn-primary"
                    type="submit"
                    onClick={handleSubmit}
                  >
                    Create Attribute
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
      </Modal>
      {/* <!-- Create Modal end --> */}

      {/* <!-- EditModal --> */}

      <Modal
        show={showEditModel}
        onHide={handleEditModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title> Update Attribute</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={editValues}
            enableReinitialize
            validationSchema={validation}
            onSubmit={DataUpdateFormSubmit}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-update"
                className="needs-validation"
                autoComplete="off"
              >
                <FormikField
                  type="hidden"
                  name="role_id"
                  id="role_id"
                  autoComplete="off"
                  className="form-control"
                />
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title" className="form-label">
                          Attribute Name in English
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title"
                          id="title"
                          placeholder="Attribute Name in English"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title_ar" className="form-label">
                          Attribute Name in Arabic
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title_ar"
                          id="title_ar"
                          placeholder="Attribute Name in Arabic"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="is_publish" className="form-label">
                          Status
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="is_publish"
                          id="is_publish"
                          className="form-select"
                          type="select"
                          options={generalStatusList}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                    data-bs-dismiss="modal"
                    onClick={handleEditModelClose}
                  >
                    Close
                  </button>
                  <button className="btn btn-primary" onClick={handleSubmit}>
                    Update Attribute
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
        {/* <Modal.Footer></Modal.Footer> */}
      </Modal>
      {/* <!-- EditModal end --> */}
    </>
  );
}
