import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../components/formikField";
import { useGetAllProductdetailListQuery } from "../../../feature/api/productDataApiSlice";
import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useMemo, useRef, useState } from "react";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import {
  useEditInventoryMutation,
  useSingleInventoryQuery,
} from "../../../feature/api/inventoryDataApiSlice";
import WebLoader from "../../../components/webLoader";
import { useSelector } from "react-redux";
import { handleCustomError } from "../../../hooks/handleCustomError";
import InventoryHistory from "../history/inventory_history";

const getValidationSchema = () => {
  const baseValidation = {
    branch_id: yup.string().required().label("Branch"),
    product_detail_id: yup.string().required().label("Product"),
    original_price: yup.number().min(0).required().label("Original Price"),
    standard_price: yup.number().required().label("Standard Price").test(
      'is-greater-or-equal',
      'Standard Price must be greater than or equal to Original Price',
      function(value) {
        return value >= this.parent.original_price;
      }
    ),
    selling_price: yup.number().min(0).required().label("Selling Price"),
    stock_quantity: yup.number().min(0).required().label("Stock Quantity"),
    stock_status: yup.string().required().label("Stock Status"),
    minimum_alert_stock_quantity: yup
      .number()
      .min(0)
      .required()
      .label("Minimum Alert Stock Quantity"),
    is_publish: yup.string().required().label("Status"),
    racks: yup.string().label("Racks Information"),
    manufacturing_date: yup.string().label("Manufacturing Date"),
    expiry_date: yup.string().label("Expiry Date"),
    other_info: yup.string().label("Other Information"),
  };
  return yup.object().shape(baseValidation);
};

export default function EditInventory() {
  const { id } = useParams();
  const inventoryId = parseInt(id);
  const navigation = useNavigate(); // Initialize the navigation hook
  const activePage = "Edit Inventory";
  const linkHref = "/dashboard";
  const currentFormValuesRef = useRef({});

  const [selectedProductId, setSelectedProductId] = useState("");
  const [selectedBranchId, setSelectedBranchId] = useState("");
  const [selectedProductDetailId, setSelectedProductDetailId] = useState("");

  /* **************** Start fetching single inventory data ******************* */
  let { data: singleInventory, isLoading: isInventoryLoading } =
    useSingleInventoryQuery({
      id: inventoryId,
    });

  const inventoryData = useMemo(() => {
    return singleInventory?.data || null;
  }, [singleInventory]);
  /* **************** Start fetching single inventory data ******************* */

  useEffect(() => {
    if (isInventoryLoading) return;

    if (!inventoryData) {
      handleCustomError("Error was found, please try again later!");
      navigation("/inventory");
    } else {
      setSelectedBranchId(inventoryData?.branch_id);
      setSelectedProductId(inventoryData?.product_id);
      setSelectedProductDetailId(inventoryData?.product_detail_id);
    }
  }, [isInventoryLoading, inventoryData, navigation]);

  const getInitialValues = (inventoryData = {}, currentValue) => {
    const baseValues = {
      branch_id: currentValue.branch_id || inventoryData?.branch_id || "",
      product_detail_id: currentValue.product_detail_id || inventoryData?.product_detail_id,
      product_id: currentValue.product_id || inventoryData?.product_id || "",
      original_price: currentValue.original_price || inventoryData?.original_price || "",
      selling_price: currentValue.selling_price || inventoryData?.selling_price || "",
      stock_quantity: currentValue.stock_quantity || inventoryData?.stock_quantity || "",
      minimum_alert_stock_quantity: currentValue.minimum_alert_stock_quantity || inventoryData?.minimum_alert_stock_quantity,
      stock_status: currentValue.stock_status || inventoryData?.stock_status || "",
      is_publish: currentValue.is_publish || inventoryData?.is_publish || "",
      racks: currentValue.racks || inventoryData?.racks || "",
      manufacturing_date: currentValue.bramanufacturing_datench_id || inventoryData?.manufacturing_date || "",
      expiry_date: currentValue.expiry_date || inventoryData?.expiry_date || "",
      other_info: currentValue.other_info || inventoryData?.other_info || "",
      standard_price: currentValue.standard_price || inventoryData?.standard_price || ""
    };

    return baseValues;
  };

  /* **************** Start Fetch Product Details (Load In Select Box) ******************* */

  const handleBranchChange = (e) => {
    setSelectedBranchId(e.target.value);
  };

  const { data: productListResp } = useGetAllProductdetailListQuery({
    branch_id: parseInt(selectedBranchId),
    skip: !selectedBranchId,
  });

  const productList = productListResp?.data || [];
  const productAry = productList.map((product) => ({
    value: product.id,
    label:
      product.product_name +
      "(barcode: " +
      product.bar_code +
      " - sku: " +
      product.sku +
      ")",
    product_key: product.product_id,
  }));
  /* **************** End Fetch Product Details (Load In Select Box) ******************* */

  /* **************** Start stock status List ******************* */
  const stockStatusList = [
    { value: 1, label: "In Stock" },
    {
      value: 2,
      label: "Out of Stock",
    },
  ];
  /* **************** End stock status List ******************* */

  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector(
    (state) => state.commonState.generalStatus
  );
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** Start list General Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  const handleProductSelection = (e) => {
    setSelectedProductDetailId(e.target.value);
    const selectedProduct = productAry.find(
      (product) => parseInt(product.value) === parseInt(e.target.value)
    );
    if (selectedProduct) {
      setSelectedProductId(selectedProduct.product_key);
    }
  };

  /* **************** Start Edit Inventory ******************* */
  const [handleEditInventoryApi, { isLoading: isEditLoading }] =
    useEditInventoryMutation();
  const handleSubmit = async (body) => {
    try {
      const attributes = [];
      Object.keys(body).forEach((key) => {
        if (key.startsWith("attribute_") && body[key] && body[key] !== "") {
          attributes.push(parseInt(body[key]));
        }
      });

      const updateData = {
        id: inventoryId,
        branch_id: parseInt(selectedBranchId),
        product_id: parseInt(selectedProductId),
        product_detail_id: parseInt(selectedProductDetailId),
        stock_quantity: parseInt(body.stock_quantity),
        minimum_alert_stock_quantity: parseInt(
          body.minimum_alert_stock_quantity
        ),
        stock_status: parseInt(body.stock_status),
        original_price: parseFloat(body.original_price),
        standard_price: parseFloat(body.standard_price), 
        selling_price: parseFloat(body.selling_price), 
        is_publish: parseInt(body.is_publish),
        racks: body.racks,
        manufacturing_date: body.manufacturing_date,
        expiry_date: body.expiry_date,
        other_info: body.other_info
      };

      const resp = await handleEditInventoryApi(updateData).unwrap();
      handleApiSuccess(resp);
      navigation("/inventory");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Esit Inventory ******************* */

  /* **************** Web Loader  ******************* */
  if (isEditLoading) return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">
                              Edit Inventory Details
                            </h4>
                            <p className="card-subtitle mb-4">
                              To update Inventory, fill details and save from
                              here
                            </p>
                            <Formik
                              initialValues={getInitialValues(inventoryData, currentFormValuesRef.current)}
                              enableReinitialize={true}
                              validationSchema={getValidationSchema()}
                              onSubmit={handleSubmit}
                            >
                               {(formikProps) => {
                                currentFormValuesRef.current = formikProps.values;                                
                                return (
                              <Form
                                name="product-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_id"
                                        className="form-label"
                                      >
                                        Branch
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        name="branch_id"
                                        id="branch_id"
                                        className="form-select"
                                        type="select"
                                        options={branchesList}
                                        onChange={handleBranchChange}
                                        disabled={true}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="product_id"
                                        className="form-label"
                                      >
                                        Product
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        name="product_id"
                                        id="product_id"
                                        className="form-select"
                                        type="select"
                                        options={productAry}
                                        onChange={handleProductSelection}
                                        disabled={true}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="original_price"
                                        className="form-label"
                                      >
                                        Original Price
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="original_price"
                                        id="original_price"
                                        placeholder="Enter orignal price *"
                                        className="form-control"
                                        step="0.01"
                                        disabled={inventoryData?.purchase_invoice_id ? true : false}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="standard_price"
                                        className="form-label"
                                      >
                                        Standard Price
                                        <span className="un-validation">
                                          (*) {" "}
                                        </span>
                                        <span className="mb-1 badge rounded-pill bg-primary-subtle text-primary"><i className="ti ti-info-circle text-primary me-2"></i>Standard price includes all associated costs, such as side expenses and shipping charges.</span>
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="standard_price"
                                        id="standard_price"
                                        placeholder=""
                                        className="form-control"
                                        disabled={inventoryData?.purchase_invoice_id ? true : false}
                                      />
                                    </div>
                                  </div>                                  
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="selling_price"
                                        className="form-label"
                                      >
                                        Selling Price
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="selling_price"
                                        id="selling_price"
                                        placeholder="Enter selling price *"
                                        className="form-control"
                                        step="0.01"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6"></div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="stock_quantity"
                                        className="form-label"
                                      >
                                        Stock Quantity
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="stock_quantity"
                                        id="stock_quantity"
                                        placeholder="Enter stock quantity *"
                                        className="form-control"
                                        min="0"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="minimum_alert_stock_quantity"
                                        className="form-label"
                                      >
                                        Minimum Alert Stock Quantity
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="minimum_alert_stock_quantity"
                                        id="minimum_alert_stock_quantity"
                                        placeholder="Enter minimum alert stock quantity *"
                                        className="form-control"
                                        min="0"
                                      />
                                    </div>
                                  </div>
                                   <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label htmlFor="manufacturing_date" className="form-label">
                                          Manufacturing Date
                                        </label>
                                        <FormikField
                                          type="date"
                                          name="manufacturing_date"
                                          id="manufacturing_date"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label htmlFor="expiry_date" className="form-label">
                                          Expiry Date
                                        </label>
                                        <FormikField
                                          type="date"
                                          name="expiry_date"
                                          id="expiry_date"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label htmlFor="racks" className="form-label">
                                          Racks Information
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="racks"
                                          id="racks"
                                          placeholder="Enter Racks Information"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label htmlFor="other_info" className="form-label">
                                          Other Information
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="other_info"
                                          id="other_info"
                                          placeholder="Other Information"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="stock_status"
                                        className="form-label"
                                      >
                                        Stock Status
                                        <span className="un-validation">
                                          (*)
                                        </span>
                                      </label>
                                      <FormikField
                                        type="select"
                                        name="stock_status"
                                        id="stock_status"
                                        className="form-select"
                                        options={stockStatusList}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_name"
                                        className="form-label"
                                      >
                                        Status
                                        <span className="un-validation">
                                          (*)
                                        </span>{" "}
                                        :
                                      </label>
                                      <FormikField
                                        name="is_publish"
                                        id="is_publish"
                                        className="form-select"
                                        type="select"
                                        options={generalStatusList}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Update Inventory
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                               );
                              }}
                            </Formik>
                            <div className="mt-4"></div>
                            <h4 className="card-title">Inventory History</h4>
                            <InventoryHistory renderTable={true}></InventoryHistory>                
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
