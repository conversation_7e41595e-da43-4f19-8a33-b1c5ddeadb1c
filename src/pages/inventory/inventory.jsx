import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { Link, useNavigate } from "react-router-dom";
import {
  useDeleteInventoryMutation,
  useGetInventoryListQuery,
} from "../../feature/api/inventoryDataApiSlice";
import { Table } from "../../components/datatable";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { useMemo, useState } from "react";
import WebLoader from "../../components/webLoader";
import { PaginationComponent } from "../../components/pagination";
import {
  useGetAllProductdetailListQuery,
  useListAllProductQuery,
} from "../../feature/api/productDataApiSlice";
import { useGetShopBranchsQuery } from "../../feature/api/branchDataApiSlice";
import useConfirm from "../../hooks/useConfirm";
import { useSelector } from "react-redux";

export default function Inventory({
  title = "Inventory List",
  productDetailId,
  renderInTab,
  filters = ["product", "product-detail", "branch", "status", "stock-status", "manufacturing-date", "expiry-date"],
  filtersValue,
}) {
  const activePage = title;
  const linkHref = "/dashboard";

  const navigation = useNavigate();
  /* **************** Start list all Inventory ******************* */
  const [currentPage, setCurrentPage] = useState(1);
  const [filterProductId, setFilterProductId] = useState(null);
  const [filterProductDetailId, setFilterProductDetailId] = useState(null);
  const [filterBranchId, setFilterBranchId] = useState(null);
  const [filterStockStatus, setFilterStockStatus] = useState(null);
  const [filterStatus, setFilterStatus] = useState("");
  const [filterKeywords, setFilterKeywords] = useState("");
  const [filterManufacturingDate, setFilterManufacturingDate] = useState("");
  const [filterExpiryDate, setFilterExpiryDate] = useState("");
  const { data: inventoryListResp, isLoading } = useGetInventoryListQuery({
    page: currentPage,
    stock_status: parseInt(filtersValue?.stock_status || filterStockStatus),
    product_id: parseInt(filterProductId),
    product_detail_id: productDetailId || parseInt(filterProductDetailId),
    branch_id: parseInt(filterBranchId),
    status: parseInt(filterStatus),
    minimum_alert_stock_quantity: filtersValue?.minimum_alert_stock_quantity,
    out_of_stock_quantity: filtersValue?.out_of_stock_quantity,
    keywords: filterKeywords,
    manufacturing_date: filterManufacturingDate,
    expiry_date: filterExpiryDate,
  });
  const inventoryList = useMemo(() => {
    if (!inventoryListResp?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return inventoryListResp?.data?.list;
  }, [currentPage, inventoryListResp?.data.list]);
  const pageData = useMemo(
    () => inventoryListResp?.data?.page ?? null,
    [inventoryListResp]
  );

  /* **************** End list all Inventory ******************* */

  /* ****************  Start Filter ****************** */
  const handleStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleStockStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterStockStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleProductFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterProductId(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleProductDetailFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterProductDetailId(parseInt(selectedValue));
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleBranchFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterBranchId(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleManufacturingDateFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterManufacturingDate(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleExpiryDateFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterExpiryDate(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start list Stock Status ******************* */
  const stockStatusList = [
    { value: 1, label: "In Stock" },
    {
      value: 2,
      label: "Out of Stock",
    },
  ];
  /* **************** End list Stock Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector(
    (state) => state.commonState.generalStatus
  );
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** Start list General Status ******************* */

  /* **************** Start Product List ******************* */
  const { data: productListResp } = useListAllProductQuery({});

  const productList = productListResp?.data || [];
  const productAry = productList.map((product) => ({
    value: product.id,
    label: product.title,
  }));
  /* **************** End Product List ******************* */

  /* **************** Start Product Detail List ******************* */
  const { data: productDetailAry } = useGetAllProductdetailListQuery({
    product_id: parseInt(filterProductId),
  });
  const productDetailList = useMemo(() => {
    if (!productDetailAry?.data?.length) return [];
    return productDetailAry.data.map((values) => ({
      value: values.id,
      label:
        values.product_name +
        "(barcode: " +
        values.bar_code +
        " - sku: " +
        values.sku +
        ")",
    }));
  }, [productDetailAry?.data]);

  /* **************** End Product Detail List ******************* */

  /* **************** End Filter ***************** */

  /* **************** Start Paginatation ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Paginatation ***************** */

  /* **************** Start Delete Inventory ***************** */
  const { showSimpleConfirm } = useConfirm({
    title: "Delete Inventory?",
    text: "Are you sure you want to delete this inventory?",
    confirmButtonText: "Yes, delete inventory!",
    cancelButtonText: "Cancel",
  });
  const [handledeleteInventoryApi, { isLoading: isDeleteLoading }] =
    useDeleteInventoryMutation();
  const onDeleteInventoryHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
    if (confirmed) {
      try {
        const body = { id: id };
        const resp = await handledeleteInventoryApi(body).unwrap();
        handleApiSuccess(resp);
        navigation("/inventory"); // Redirect to the desired page
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };

  /* **************** End Delete Inventory ***************** */

  const onEditInventoryDetailsHandler = (d) => {
    navigation(`/editInventory/${d.id}`);
  };

  /* **************** Web Loader  ******************* */
  if (isLoading || isDeleteLoading) return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  // render if render in tab
  if (renderInTab) {
    return (
      <div className="card-body p-4">
        <div className="table-responsive">
          <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
            <div className="d-flex gap-6">
              {filters.includes("product") && (
                <div>
                  <select
                    value={filterProductId}
                    className="form-control search-chat py-2"
                    onChange={handleProductFilter}
                  >
                    <option value="">All Products</option>
                    {productAry.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}
              {filters.includes("product-detail") && (
                <div>
                  <select
                    value={filterProductDetailId}
                    className="form-control search-chat py-2"
                    onChange={handleProductDetailFilter}
                  >
                    <option value="">All Product Details</option>
                    {productDetailList.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}
              {filters.includes("branch") && (
                <div>
                  <select
                    value={filterBranchId}
                    className="form-control search-chat py-2"
                    onChange={handleBranchFilter}
                  >
                    <option value="">All Branches</option>
                    {branchesList.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}
              {filters.includes("stock-status") && (
                <div>
                  <select
                    value={filterStockStatus}
                    className="form-control search-chat py-2"
                    onChange={handleStockStatusFilter}
                  >
                    <option value="">All Stock Status</option>
                    {stockStatusList.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}
              {filters.includes("status") && (
                <div>
                  <select
                    value={filterStatus}
                    className="form-control search-chat py-2"
                    onChange={handleStatusFilter}
                  >
                    <option value="">All Status</option>
                    {generalStatusList.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}                  
              {filters.includes("manufacturing-date") && (
              <div className="">
                <input
                  type="date"
                  id="manufacturing-date"
                  className="form-control search-chat py-2"
                  onChange={handleManufacturingDateFilter}
                />
                <small
                  className="text-muted"
                >
                  Filter by manufacturing date
                </small>
              </div>
            )}
            {filters.includes("expiry-date") && (
              <div className="">
                <input
                  type="date"
                  id="expiry-date"
                  className="form-control search-chat py-2"
                  onChange={handleExpiryDateFilter}
                />
                <small
                  className="text-muted"
                >
                  Filter by expiry date
                </small>
              </div>
            )}
            </div>
            <div className="position-relative">
              <input
                type="text"
                className="form-control search-chat py-2 ps-5"
                id="text-srh"
                onChange={handleKeywordsFilter}
                placeholder="Keyword Search..."
                value={filterKeywords}
              />
              <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
            </div>
          </div>
          <Table
            headCells={[
              { key: "sel_id", label: "#", align: "left" },
              {
                key: "branch_name",
                label: "Branch",
                align: "left",
                linkTo: (row) => ({
                  to: `/editBranch/${row.branch_id}`,
                }),
              },
              {
                key: "org_product_name",
                label: "Product",
                align: "left",
                linkTo: (row) => ({
                  to: `/viewProductDetail/${row.product_detail_id}`,
                }),
              },
              {
                key: "original_price_with_currency_code",
                label: "Orignal Price",
                align: "left",
              },
              {
                key: "standard_price_with_currency_code",
                label: "STD Price",
                align: "left",
              },              
              {
                key: "selling_price_with_currency_code",
                label: "Price",
                align: "left",
              },
              {
                key: "stock_quantity",
                label: "Stock Quantity",
                align: "left",
              },
              {
                key: "stock_status_text",
                key_id: "stock_status",
                label: "Stock",
                align: "left",
              },
              {
                key: "status_name",
                key_id: "status",
                label: "Status",
                align: "left",
              },
            ]}
            data={inventoryList}
            onDeleteHandler={onDeleteInventoryHandler}
            onEditHandler={onEditInventoryDetailsHandler}
          />
          <PaginationComponent
            totalCount={pageData?.total_count}
            pageSize={pageData?.page_size}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            onPageChange={fetchData}
          />
        </div>
      </div>
    );
  }

  // return if not rendered in tab
  return (
    <div className="page-wrapper">
      <TopBar />
      <div className="body-wrapper">
        <div className="container-fluid mw-100">
          <Breadcrumb activePage={activePage} linkHref={linkHref} />
          <div className="card w-100 position-relative overflow-hidden">
            <div className="px-4 py-3 border-bottom">
              <div className="d-sm-flex align-items-center justify-space-between">
                <h4 className="card-title mb-0">{title}</h4>
                <nav aria-label="breadcrumb" className="ms-auto">
                  <ol className="breadcrumb">
                    <li className="breadcrumb-item" aria-current="page">
                      <Link
                        type="button"
                        to={"/createInventory"}
                        className="btn btn-primary"
                      >
                        Add Inventory
                      </Link>
                    </li>
                  </ol>
                </nav>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="table-responsive">
                <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                  <div className="d-flex gap-6">
                    {filters.includes("product") && (
                      <div>
                        <select
                          value={filterProductId}
                          className="form-control search-chat py-2"
                          onChange={handleProductFilter}
                        >
                          <option value="">All Products</option>
                          {productAry.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                    {filters.includes("product-detail") && (
                      <div>
                        <select
                          value={filterProductDetailId}
                          className="form-control search-chat py-2"
                          onChange={handleProductDetailFilter}
                        >
                          <option value="">All Product Details</option>
                          {productDetailList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                    {filters.includes("branch") && (
                      <div>
                        <select
                          value={filterBranchId}
                          className="form-control search-chat py-2"
                          onChange={handleBranchFilter}
                        >
                          <option value="">All Branches</option>
                          {branchesList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                    {filters.includes("stock-status") && (
                      <div>
                        <select
                          value={filterStockStatus}
                          className="form-control search-chat py-2"
                          onChange={handleStockStatusFilter}
                        >
                          <option value="">All Stock Status</option>
                          {stockStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                    {filters.includes("status") && (
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2"
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {generalStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}                  
                    {filters.includes("manufacturing-date") && (
                    <div className="">
                      <input
                        type="date"
                        id="manufacturing-date"
                        className="form-control search-chat py-2"
                        onChange={handleManufacturingDateFilter}
                      />
                      <small
                        className="text-muted"
                      >
                        Filter by manufacturing date
                      </small>
                    </div>
                  )}
                  {filters.includes("expiry-date") && (
                    <div className="">
                      <input
                        type="date"
                        id="expiry-date"
                        className="form-control search-chat py-2"
                        onChange={handleExpiryDateFilter}
                      />
                      <small
                        className="text-muted"
                      >
                        Filter by expiry date
                      </small>
                    </div>
                  )}
                  </div>
                  <div className="position-relative">
                    <input
                      type="text"
                      className="form-control search-chat py-2 ps-5"
                      id="text-srh"
                      onChange={handleKeywordsFilter}
                      placeholder="Keyword Search..."
                      value={filterKeywords}
                    />
                    <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                  </div>
                </div>
                <Table
                  headCells={[
                    { key: "sel_id", label: "#", align: "left" },
                    {
                      key: "branch_name",
                      label: "Branch",
                      align: "left",
                      linkTo: (row) => ({
                        to: `/editBranch/${row.branch_id}`,
                      }),
                    },
                    {
                      key: "org_product_name",
                      label: "Product",
                      align: "left",
                      linkTo: (row) => ({
                        to: `/viewProductDetail/${row.product_detail_id}`,
                      }),
                    },
                    {
                      key: "original_price_with_currency_code",
                      label: "Orignal Price",
                      align: "left",
                    },
                    {
                      key: "standard_price_with_currency_code",
                      label: "STD Price",
                      align: "left",
                    },                    
                    {
                      key: "selling_price_with_currency_code",
                      label: "Price",
                      align: "left",
                    },
                    {
                      key: "stock_quantity",
                      label: "Stock Quantity",
                      align: "left",
                    },
                    {
                      key: "stock_status_text",
                      key_id: "stock_status",
                      label: "Stock",
                      align: "left",
                    },
                    {
                      key: "status_name",
                      key_id: "status",
                      label: "Status",
                      align: "left",
                    },
                  ]}
                  data={inventoryList}
                  onDeleteHandler={onDeleteInventoryHandler}
                  onEditHandler={onEditInventoryDetailsHandler}
                />
                <PaginationComponent
                  totalCount={pageData?.total_count}
                  pageSize={pageData?.page_size}
                  currentPage={currentPage}
                  setCurrentPage={setCurrentPage}
                  onPageChange={fetchData}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
