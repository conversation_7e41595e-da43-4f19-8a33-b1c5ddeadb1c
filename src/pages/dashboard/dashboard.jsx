import TopBar from "../../components/layout/topBar";
import Chart from "react-apexcharts";
import {
  useGetDashboardCardCountQuery,
  useGetDashboardInventoryChartDataQuery,
  useGetDashboardInventoryChartYearsQuery,
} from "../../feature/api/dashboardDataApiSlice";
import { useNavigate } from "react-router-dom";
import { useState, useEffect, useMemo } from "react";

export default function Dashboard() {
  const navigate = useNavigate();

  /* **************** Start list inventory chart years ******************* */
  const chartYearsData = useGetDashboardInventoryChartYearsQuery();
  const chartYearsDataList = useMemo(() =>
    chartYearsData?.data?.data || [],
    [chartYearsData?.data?.data]
  );
  const chartYearsList = useMemo(() =>
    chartYearsDataList.map((values) => ({
      value: values,
      label: values,
    })),
    [chartYearsDataList]
  );

  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  useEffect(() => {
    if (chartYearsDataList.length > 0 && !chartYearsDataList.includes(selectedYear)) {
      setSelectedYear(chartYearsDataList[0]);
    }
  }, [chartYearsDataList, selectedYear]);
  /* **************** End list inventory chart years ******************* */

  const {
    data: inventoryChartData,
    isLoading: isChartLoading,
    error: chartError,
  } = useGetDashboardInventoryChartDataQuery({ year: String(selectedYear) });
  const inventoryChartDataSeries = inventoryChartData?.data || {};

  const chartOptions = {
    chart: {
      id: "basic-line",
      toolbar: {
        show: true,
      },
    },
    xaxis: {
      categories: inventoryChartDataSeries?.categories || [],
      title: {
        text: "Month",
      },
    },
    yaxis: {
      title: {
        text: "Inventory",
      },
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    colors: ["#3b82f6"],
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      theme: "light",
    },
  };


  const chartSeries = [
    {
      name: inventoryChartDataSeries?.chartSeries?.name,
      data: inventoryChartDataSeries?.chartSeries?.data || []
    }
  ];

  /* **************** Start dashboard card count ******************* */

  const cardCountData = useGetDashboardCardCountQuery();
  const cardCount = cardCountData?.data || [];
  const inventoryCount = cardCount?.data?.count?.inventory_count;
  const branchCount = cardCount?.data?.count?.branch_count;
  const staffCount = cardCount?.data?.count?.staff_count;
  const staffRolesCount = cardCount?.data?.count?.staff_rols_count;
  const categoryCount = cardCount?.data?.count?.categories_count;
  const subCategoryCount = cardCount?.data?.count?.sub_categories_count;
  const childCategoryCount = cardCount?.data?.count?.child_categories_count;
  const productCount = cardCount?.data?.count?.products_count;
  const attributeCount = cardCount?.data?.count?.attributes_count;
  const attributeValueCount = cardCount?.data?.count?.attributes_value_count;
  const brandCount = cardCount?.data?.count?.brands_count;

  /* **************** End dashboard card count ******************* */

  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid">
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-body p-4 pb-0" data-simplebar="">
                    <div className="row flex-nowrap">
                      <div className="col">
                        <div className="card primary-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-primary flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="solar:dollar-minimalistic-linear"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Branches
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {branchCount}
                            </h4>
                            <a
                              onClick={() => navigate("/branch_list")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card warning-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-warning flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="solar:recive-twice-square-linear"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">Staff Roles</h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {staffRolesCount}
                            </h4>
                            <a
                              onClick={() => navigate("/roles")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card secondary-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-secondary flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-backpack"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Staff
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {staffCount}
                            </h4>
                            <a
                              onClick={() => navigate("/staffs")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card danger-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-danger flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:baseline-sync-problem"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Categories
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {categoryCount}
                            </h4>
                            <a
                              onClick={() => navigate("/mainCategories")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card success-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-success flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-forest"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Sub Categories
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {subCategoryCount}
                            </h4>
                            <a
                              onClick={() => navigate("/subCategories")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card primary-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-primary flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-forest"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Child Categories
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {childCategoryCount}
                            </h4>
                            <a
                              onClick={() => navigate("/childCategories")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card warning-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-warning flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-forest"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Attributes
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {attributeCount}
                            </h4>
                            <a
                              onClick={() => navigate("/attributes")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card danger-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-danger flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-forest"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Attribute Values
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {attributeValueCount}
                            </h4>
                            <a
                              onClick={() => navigate("/attributesValues")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card secondary-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-secondary flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-forest"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Brands
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {brandCount}
                            </h4>
                            <a
                              onClick={() => navigate("/brands")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card success-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-success flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-forest"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Product
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {productCount}
                            </h4>
                            <a
                              onClick={() => navigate("/products")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                      <div className="col">
                        <div className="card primary-gradient">
                          <div className="card-body text-center px-9 pb-4">
                            <div className="d-flex align-items-center justify-content-center round-48 rounded text-bg-primary flex-shrink-0 mb-3 mx-auto">
                              <iconify-icon
                                icon="ic:outline-forest"
                                className="fs-7 text-white"
                              ></iconify-icon>
                            </div>
                            <h6 className="fw-normal fs-3 mb-1">
                              Inventory
                            </h6>
                            <h4 className="mb-3 d-flex align-items-center justify-content-center gap-1">
                              {inventoryCount}
                            </h4>
                            <a
                              onClick={() => navigate("/inventory")}
                              className="btn btn-white fs-2 fw-semibold text-nowrap"
                            >
                              View
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-body">
                    <div className="d-flex justify-content-between align-items-center mb-4">
                      <h5 className="card-title mb-0">Inventory</h5>
                      <div className="d-flex align-items-center">
                        <label htmlFor="yearSelect" className="form-label me-2 mb-0">
                          Year:
                        </label>
                        <select
                          id="yearSelect"
                          className="form-select"
                          style={{ width: "120px" }}
                          value={selectedYear}
                          onChange={(e) => setSelectedYear(e.target.value)}
                        >
                          {chartYearsList.map((year) => (
                            <option key={year.value} value={year.value}>
                              {year.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    {isChartLoading ? (
                      <div className="d-flex justify-content-center align-items-center" style={{ height: '350px' }}>
                        <div className="spinner-border text-primary" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </div>
                    ) : chartError ? (
                      <div className="d-flex justify-content-center align-items-center" style={{ height: '350px' }}>
                        <div className="text-danger">
                          <i className="fas fa-exclamation-triangle me-2"></i>
                          Error loading chart data
                        </div>
                      </div>
                    ) : (
                      <Chart
                        options={chartOptions}
                        series={chartSeries}
                        type="line"
                        height={350}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}