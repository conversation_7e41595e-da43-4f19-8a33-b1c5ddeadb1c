import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { useNavigate } from "react-router-dom";
import {
  useDeleteShopBranchMutation,
  useGetShopBranchsQuery,
} from "../../feature/api/branchDataApiSlice";
import { Table } from "../../components/datatable";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { useState } from "react";
import useConfirm from "../../hooks/useConfirm";
import WebLoader from "../../components/webLoader";
import { useSelector } from "react-redux";

export default function BranchList() {
  const activePage = "Branches Master";
  const linkHref = "/dashboard";

  const [filterStatus, setFilterStatus] = useState(null);
  const [filterBranchType, setFilterBranchType] = useState(null);
  const [filterKeywords, setFilterKeywords] = useState("");

  const branchListResp = useGetShopBranchsQuery({
    is_publish: parseInt(filterStatus),
    branch_type: parseInt(filterBranchType),
    keywords: filterKeywords
  });
  const branchList = branchListResp.data?.data || [];
  const navigation = useNavigate();

   /* ****************  Start Filter ****************** */
    const handleStatusFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterStatus(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleBranchTypeFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterBranchType(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleKeywordsFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterKeywords(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    /* **************** End Filter ***************** */

    /* **************** Start list Branch Status ******************* */
    const branchStatusData = useSelector((state) => state.commonState.branchStatus);
    const branchStatusDataList = branchStatusData?.data || [];
    const branchStatusList = branchStatusDataList.map((values) => ({
      value: values.id,
      label: values.status,
    }));
    /* **************** Start list Branch Status ******************* */

    /* **************** Start list Branch Types ******************* */
    const branchTypesData = useSelector((state) => state.commonState.branchTypes);
    const branchTypesDataList = branchTypesData?.data || [];
    const branchTypesList = branchTypesDataList.map((values) => ({
      value: values.id,
      label: values.branch_type,
    }));
    /* **************** Start list Branch Types ******************* */

    /* **************** Start Delete Branch Types ******************* */

    const { showSimpleConfirm } = useConfirm({
      title: 'Delete Branch?',
      text: 'Are you sure you want to delete this branch?',
      confirmButtonText: 'Yes, delete branch!',
      cancelButtonText: 'Cancel'
    });
    
    const [handledeleteBranchApi, {isLoading: isDeleteBranchLoading}] = useDeleteShopBranchMutation();
    
    const onDeleteBranchHandler = async (id) => {
      const confirmed = await showSimpleConfirm();
      if (confirmed) {
        try {
          const body = { branch_id: id };
          const resp = await handledeleteBranchApi(body).unwrap();
          handleApiSuccess(resp);
          navigation("/branch_list"); // Redirect to the desired page
        } catch (error) {
          handleApiErrors(error);
        }
      }
    };

    /* **************** End Delete Branch Types ******************* */
    
  const onEditBranchDetailsHandler = (d) => {
    navigation(`/editBranch/${d.id}`);
  };

  /* **************** Start Web Loader  ******************* */
            if (isDeleteBranchLoading)
              return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Branch List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      {/* <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          to={"/createBranch"}
                          className="btn btn-primary"
                        >
                          Create New Branch
                        </Link>
                      </li> */}
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2 "
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {branchStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterBranchType}
                          className="form-control search-chat py-2 "
                          onChange={handleBranchTypeFilter}
                        >
                          <option value="">All Branch Type</option>
                          {branchTypesList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "id", label: "#", align: "left" },
                      {
                        key: "branch_type_name",
                        label: "Branch Type",
                        align: "left",
                      },
                      {
                        key: "branch_name",
                        label: "Branch Name",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/editBranch/${row.id}`,
                        }),
                      },
                      {
                        key: "branch_ful_phone",
                        label: "Branch Phone Number",
                        align: "left",
                      },
                      {
                        key: "branch_email",
                        label: "Branch Email",
                        align: "left",
                      },
                      {
                        key: "branch_location",
                        label: "Branch Location",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                    ]}
                    data={branchList}
                    onDeleteHandler={onDeleteBranchHandler}
                    onEditHandler={onEditBranchDetailsHandler}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
