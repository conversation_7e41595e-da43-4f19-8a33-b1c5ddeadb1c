import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { useMemo, useState } from "react";
import { Table } from "../../components/datatable";
import { PaginationComponent } from "../../components/pagination";
import { useGetCustomerContactsListQuery, useCreateCustomerContactsMutation, useDeleteCustomerContactsMutation, useEditCustomerContactsMutation } from "../../feature/api/customerContactsDataApiSlice";
import WebLoader from "../../components/webLoader";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { Modal } from "react-bootstrap";
import { Formik } from "formik";
import * as yup from "yup";
import { Form } from "react-router-dom";
import FormikField from "../../components/formikField";
import { useListAllCustomerQuery } from "../../feature/api/customerDataApiSlice";
import useConfirm from "../../hooks/useConfirm";
import { useGetShopBranchsQuery } from "../../feature/api/branchDataApiSlice";
import { useSelector } from "react-redux";

const initialValues = {
  branch_id: "",
  customer_id: "",
  contact_name: "",
  phone_code: "",
  phone: "",
  email: "",
  remarks: "",
};

const validation = yup.object().shape({
  customer_id: yup.string().required().label("Customer"),
  contact_name: yup.string().required().label("Contact Name"),
  phone_code: yup.string().required().label("Phone Code"),
  phone: yup.string().required().label("Phone"),
  email: yup.string().email().label("Email"),
  remarks: yup.string().label("Remarks"),
});

export default function CustomerContacts() {
  const activePage = "Customer Contacts";
  const linkHref = "/dashboard";
  
  /* **************** Start list all countries ******************* */

  const countries = useSelector((state) => state.commonState.countries);
  const countriesList = countries?.data
    ? countries.data.map((values) => ({
        value: values.phone_code,
        label: values.name + ' (' + values.phone_code + ')',
      }))
    : [];
  /* **************** End list all countries ******************* */

  /* **************** Data Add Model ******************* */
  const [showAddModel, setShowAddModel] = useState(false);
  const handleAddModelClose = () => setShowAddModel(false);
  const handleAddModelShow = () => setShowAddModel(true);
  /* **************** End Data Add Model ******************* */

  /* **************** Data Edit Model ******************* */
  const [showEditModel, setShowEditModel] = useState(false);
  const handleEditModelClose = () => setShowEditModel(false);
  const handleEditModelShow = () => setShowEditModel(true);
  /* **************** End Data Edit Model ******************* */

  /* **************** Start list Customers ******************* */
  const customersData = useListAllCustomerQuery();

  const customersDataList = customersData?.data?.data || [];
  const customersListFilter = customersDataList.map((values) => ({
    value: values.id,
    label: values.customer_name,
  }));
  /* **************** End list Customers ******************* */
  /* **************** Start list all Customer Contact ******************* */
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedBranchId, setSelectedBranchId] = useState("");
  const [selectedEditBranchId, setSelectedEditBranchId] = useState("");

  const [filterCustomerId, setFilterCustomerId] = useState(null);
  const [filterKeywords, setFilterKeywords] = useState("");
  /* **************** Start list all Customer Contacts ******************* */
  const { data: customerContactsData, isLoading } = useGetCustomerContactsListQuery({
    page: currentPage,
    customer_id: parseInt(filterCustomerId),
    keywords: filterKeywords,
  });
  const customerContactsList = useMemo(() => {
    if (!customerContactsData?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return customerContactsData?.data?.list;
  }, [currentPage, customerContactsData?.data.list]);

  const pageData = useMemo(
    () => customerContactsData?.data?.page ?? null,
    [customerContactsData]
  );

  /* **************** End list all Customer Contacts ******************* */
  /* ****************  Start Filter ****************** */
  const handleCustomerFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterCustomerId(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

    /* **************** Start Branch List ******************* */
    const branchListResp = useGetShopBranchsQuery();
    const branchList = branchListResp.data?.data || [];
    const branchesList = branchList.map((values) => ({
      value: values.id,
      label: values.branch_name + ' (' + values.branch_type_name + ')',
    }));
    /* **************** End Branch List ******************* */

    /* **************** Start Fetch Customer Based On Branch (Load in Select Box) ******************* */
      
      const { data: customerData } = useListAllCustomerQuery(
        { branch_id: selectedBranchId },
        { skip: selectedBranchId === "" }
      );
      const customerList = useMemo(() => {
        if (!customerData?.data?.length) {
          return [];
        }
        return customerData.data.map((values) => ({
          value: values.id,
          label: values.customer_name,
        }));
      }, [customerData?.data]);

      /* **************** Start Fetch Customer Based On Branch for Edit Modal ******************* */
      const { data: customerEditData } = useListAllCustomerQuery(
        { branch_id: selectedEditBranchId },
        { skip: selectedEditBranchId === "" }
      );
      const customerEditList = useMemo(() => {
        if (!customerEditData?.data?.length) {          
          return [];
        }
        const list = customerEditData.data.map((values) => ({
          value: values.id,
          label: values.customer_name,
        }));        
        return list;
      }, [customerEditData?.data]);
      /* **************** End Fetch Customer Based On Branch for Edit Modal ******************* */

      /* **************** End Fetch Customer Based On Branch (Load in Select Box) ******************* */
      
      /* **************** Start Handle Branch selection change in Select Box ******************* */
      const handleBranchChange = (e) => {
        const value = e.target.value === "" ? "" : parseInt(e.target.value);
        setSelectedBranchId(value);
      };

      const handleEditBranchChange = (e, setFieldValue) => {
        const value = e.target.value === "" ? "" : parseInt(e.target.value);
        setSelectedEditBranchId(value);
        // Reset customer selection when branch changes
        if (setFieldValue) {
          setFieldValue('customer_id', '');
        }
      };

    /* **************** End  Handle Branch selection change in Select Box ******************* */


  /* **************** Start Delete Customer Contact ******************* */

  const { showSimpleConfirm } = useConfirm({
        title: 'Delete Customer Contact?',
        text: 'Are you sure you want to delete this customer contact?',
        confirmButtonText: 'Yes, delete customer contact!',
        cancelButtonText: 'Cancel'
    });

  const [handledeleteDataApi, { isLoading: isDeleteLoading }] =
    useDeleteCustomerContactsMutation();
  const onDeleteDataHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
      if (confirmed) {
        try {
          const body = { contact_id: id };
          const resp = await handledeleteDataApi(body).unwrap();
          handleApiSuccess(resp);
        } catch (error) {
          handleApiErrors(error);
        }
      }
  };
  /* **************** End Delete Customer Contact ******************* */

  /* **************** Start Create Customer Contact ******************* */
  const [handleCreateDataApi, { isLoading: isCreateLoading }] =
    useCreateCustomerContactsMutation();
  const handleCreateFormSubmitFunction = async (body) => {
    try {
      const updatedBody = {
        ...body,
        customer_id: parseInt(body.customer_id),
      };
      const resp = await handleCreateDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleAddModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create  Customer Contact ******************* */
  /* **************** Start Edit Customers ******************* */
  const [editValues, setEditValues] = useState({
    branch_id: "",
    customer_id: "",
    contact_name: "",
    phone_code: "",
    phone: "",
    email: "",
    remarks: "",
  });
  const handleOpenModal = (values) => {
    setEditValues(values);
    handleEditModelShow();
  };
  const onEditDataDetailsHandler = (d) => {
    setSelectedEditBranchId(d?.branch_id ? parseInt(d.branch_id) : "");
    handleOpenModal({
      branch_id: d?.branch_id || "",
      customer_id: d?.customer_id ? parseInt(d.customer_id) : "",
      id: d?.id || "",
      contact_name: d?.contact_name || "",
      phone_code: d?.phone_code || "",
      phone: d?.phone || "",
      email: d?.email || "",
      remarks: d?.remarks || "",
    });
  };

  const [handleEditDataApi, { isLoading: isEditLoading }] =
    useEditCustomerContactsMutation();
  const DataUpdateFormSubmit = async (body) => {        
    try {
      const updatedBody = {
        ...body,
        contact_id: parseInt(body.id),
        customer_id: parseInt(body.customer_id),
      };      
      const resp = await handleEditDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleEditModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** end Edit Customers ******************* */
  /* **************** Web Loader  ******************* */
  if (isLoading || isDeleteLoading || isCreateLoading || isEditLoading)
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Customer Contact List</h4>

                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <button
                          type="button"
                          className="btn btn-primary"
                          onClick={handleAddModelShow}
                        >
                          Create Customer Contact
                        </button>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                  <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">
                      <div>
                        <select
                          value={filterCustomerId}
                          className="form-control search-chat py-2 "
                          onChange={handleCustomerFilter}
                        >
                          <option value="">All Customers</option>
                          {customersListFilter.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "customer_code",
                        label: "Customer Code",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/viewCustomer/${row.customer_id}`,
                        }),
                      }, 
                      {
                        key: "contact_name",
                        label: "Contact Name",
                        align: "left",
                      },
                      {
                        key: "phone",
                        label: "Phone",
                        align: "left",
                      },
                      {
                        key: "email",
                        label: "Email",
                        align: "left",
                      },
                      {
                        key: "created_at",
                        label: "Created At",
                        align: "left",
                      },
                    ]}
                    data={customerContactsList}
                    onDeleteHandler={onDeleteDataHandler}
                    onEditHandler={onEditDataDetailsHandler}
                  />
                  <PaginationComponent
                    totalCount={pageData?.total_count}
                    pageSize={pageData?.page_size}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    onPageChange={fetchData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Create Modal --> */}

      <Modal
        show={showAddModel}
        onHide={handleAddModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title>Create Customer Contact </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={initialValues}
            validationSchema={validation}
            onSubmit={handleCreateFormSubmitFunction}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-create"
                className="needs-validation"
                autoComplete="off"
              >
                <div className="modal-body">
                  <div className="row">
                  <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="branch_id" className="form-label">
                          Branch
                        </label>
                        <FormikField
                          name="branch_id"
                          id="branch_id"
                          className="form-select"
                          type="select"
                          options={branchesList}
                          onChange={handleBranchChange}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="customer_id" className="form-label">
                          Customer
                          <span className="un-validation">(*)</span>
                        </label>
                        <FormikField
                          name="customer_id"
                          id="customer_id"
                          className="form-select"
                          type="select"
                          options={customerList}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label
                          htmlFor="contact_name"
                          className="form-label"
                        >
                          Contact Name
                          <span className="un-validation">(*)</span>
                        </label>
                        <FormikField
                          type="text"
                          name="contact_name"
                          id="contact_name"
                          placeholder="Contact Name *"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label className="form-label">
                          Phone Number{" "}
                          <span className="un-validation">(*)</span>
                        </label>
                        <div className="row">
                          <div className="col-4">
                            <FormikField
                              name="phone_code"
                              id="phone_code"
                              className="form-select"
                              type="select"
                              options={countriesList}
                            />
                          </div>
                          <div className="col-8">
                            <FormikField
                              type="number"
                              name="phone"
                              id="phone"
                              placeholder="Phone Number *"
                              autoComplete="off"
                              className="form-control"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label
                          htmlFor="email"
                          className="form-label"                                
                        >
                          Email
                        </label>
                        <FormikField
                          type="text"
                          name="email"
                          id="email"
                          placeholder="Email"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label
                          htmlFor="remarks"
                          className="form-label"
                        >
                          Remarks
                        </label>
                        <FormikField
                          type="textarea"
                          name="remarks"
                          id="remarks"
                          placeholder="Remarks"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                    data-bs-dismiss="modal"
                    onClick={handleAddModelClose}
                  >
                    Close
                  </button>
                  <button
                    className="btn btn-primary"
                    type="submit"
                    onClick={handleSubmit}
                  >
                    Create Customer Contact
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
      </Modal>
      {/* <!-- Create Modal end --> */}
      {/* <!-- EditModal --> */}

      <Modal
        show={showEditModel}
        onHide={handleEditModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title> Update Customer Contact</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={editValues}
            enableReinitialize
            validationSchema={validation}
            onSubmit={DataUpdateFormSubmit}
          >
            {({ handleSubmit, setFieldValue }) => {              
              return (
              <Form
                name="role-update"
                className="needs-validation"
                autoComplete="off"
              >
                <FormikField
                  type="hidden"
                  name="id"
                  id="id"
                  autoComplete="off"
                  className="form-control"
                />
                <div className="modal-body">
                  <div className="row">
                  <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="branch_id" className="form-label">
                          Branch
                        </label>
                        <FormikField
                          name="branch_id"
                          id="branch_id"
                          className="form-select"
                          type="select"
                          options={branchesList}
                          onChange={(e) => handleEditBranchChange(e, setFieldValue)}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="customer_id" className="form-label">
                          Customer
                          <span className="un-validation">(*)</span>
                        </label>
                        {customerEditList.length > 0 ? (
                          <FormikField
                            name="customer_id"
                            id="customer_id"
                            className="form-select"
                            type="select"
                            options={customerEditList}
                          />
                        ) : (
                          <select className="form-select" disabled>
                            <option>Loading customers...</option>
                          </select>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="contact_name" className="form-label">
                          Contact Name
                          <span className="un-validation">(*)</span>
                        </label>
                        <FormikField
                          type="text"
                          name="contact_name"
                          id="contact_name"
                          placeholder="Contact Name"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="phone" className="form-label">
                          Phone Number{" "}
                          <span className="un-validation">(*)</span> :
                        </label>
                        <div className="row">
                          <div className="col-4">
                            <FormikField
                              name="phone_code"
                              id="phone_code"
                              className="form-select"
                              type="select"
                              options={countriesList}
                            />
                          </div>
                          <div className="col-8">
                            <FormikField
                              type="number"
                              name="phone"
                              id="phone"
                              placeholder="Phone Number"
                              autoComplete="off"
                              className="form-control"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="email" className="form-label">
                          Email
                        </label>
                        <FormikField
                          type="text"
                          name="email"
                          id="email"
                          placeholder="Email"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="remarks" className="form-label">
                          Remarks
                        </label>
                        <FormikField
                          type="textarea"
                          name="remarks"
                          id="remarks"
                          placeholder="Remarks"
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                    data-bs-dismiss="modal"
                    onClick={handleEditModelClose}
                  >
                    Close
                  </button>
                  <button className="btn btn-primary" onClick={handleSubmit}>
                    Update Customer Contact
                  </button>
                </div>
              </Form>
              );
            }}
          </Formik>
        </Modal.Body>
        {/* <Modal.Footer></Modal.Footer> */}
      </Modal>
      {/* <!-- EditModal end --> */}
    </>
  );
}
