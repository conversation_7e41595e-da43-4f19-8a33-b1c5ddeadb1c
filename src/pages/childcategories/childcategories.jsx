import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import {
  useCreateChildCategoriesMutation,
  useDeleteChildCategoriesMutation,
  useEditChildCategoriesMutation,
  useGetChildCategoriesQuery,
} from "../../feature/api/childCategoriesDataApiSlice";
import { useMemo, useState } from "react";
import { PaginationComponent } from "../../components/pagination";
import { Table } from "../../components/datatable";
import { Modal } from "react-bootstrap";
import { Form } from "react-router-dom";
import { Formik } from "formik";
import FormikField from "../../components/formikField";
import * as yup from "yup";
import WebLoader from "../../components/webLoader";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { useListAllCategoriesQuery } from "../../feature/api/categoriesDataApiSlice";
import { useGetAllSubCategoriesQuery } from "../../feature/api/subCategoriesDataApiSlice";
import useConfirm from "../../hooks/useConfirm";
import { useSelector } from "react-redux";

const initialValues = {
  category_id: "",
  sub_category_id: "",
  title: "",
  title_ar: "",
  is_publish: "",
};

const validation = yup.object().shape({
  category_id: yup.string().required().label("Category"),
  sub_category_id: yup.string().required().label("Sub Category"),
  title: yup.string().required().label("Child Category Name in English"),
  title_ar: yup.string().required().label("Child Category Name in Arabic"),
  is_publish: yup.string().required().label("Child Category Status"),
});
export default function ChildCategories() {
  const activePage = "Child Categories";
  const linkHref = "/dashboard";

  /* **************** Data Add Model ******************* */
  const [showAddModel, setShowAddModel] = useState(false);
  const handleAddModelClose = () => setShowAddModel(false);
  const handleAddModelShow = () => setShowAddModel(true);
  /* **************** End Data Add Model ******************* */

  /* **************** Data Edit Model ******************* */
  const [showEditModel, setShowEditModel] = useState(false);
  const handleEditModelClose = () => setShowEditModel(false);
  const handleEditModelShow = () => setShowEditModel(true);
  /* **************** End Data Edit Model ******************* */

  /* **************** Start list all Child Categories ******************* */
  const [currentPage, setCurrentPage] = useState(1);
  const [filterCategoryId, setFilterCategoryId] = useState(null);
  const [filterSubCategoryId, setFilterSubCategoryId] = useState(null);
  const [filterStatus, setFilterStatus] = useState("");
  const [filterKeywords, setFilterKeywords] = useState("");
  const { data: childCategoriesData, isLoading } = useGetChildCategoriesQuery({
    page: currentPage,
    status: parseInt(filterStatus),
    category_id: parseInt(filterCategoryId),
    sub_category_id: parseInt(filterSubCategoryId),
    keywords: filterKeywords,
  });
  const childCategoriesList = useMemo(() => {
    if (!childCategoriesData?.data.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return childCategoriesData?.data?.list;
  }, [currentPage, childCategoriesData?.data.list]);
  const pageData = useMemo(
    () => childCategoriesData?.data?.page ?? null,
    [childCategoriesData]
  );
  /* **************** End list all Child Categories ******************* */

  /* ****************  Start Filter ****************** */
  const handleStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleCategoryFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterCategoryId(selectedValue);
      handleFilterCategoryChange(event)
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleSubCategoryFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterSubCategoryId(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchData = async (page) => {
    console.log(`Fetching data for page ${page}`);
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector((state) => state.commonState.generalStatus);
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list General Status ******************* */

  /* **************** Start GET All Categories ******************* */
  const categoriesData = useListAllCategoriesQuery();
  const categoriesDataList = categoriesData?.data?.data || [];
  const categoriesList = categoriesDataList.map((values) => ({
    value: values.id,
    label: values.title,
  }));
  /* **************** End GET  All Categories ******************* */

  /* **************** Start Create Child Categories ******************* */
  const [handleCreateDataApi, { isLoading: isCreateLoading }] =
    useCreateChildCategoriesMutation();
  const handleCreateFormSubmitFunction = async (body) => {
    try {
      const updatedBody = {
        ...body,
        is_publish: parseInt(body.is_publish),
        category_id: parseInt(body.category_id),
        sub_category_id: parseInt(body.sub_category_id),
      };
      const resp = await handleCreateDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleAddModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create Child Categories ******************* */

  /* **************** Start Delete Child Categories ******************* */

  const { showSimpleConfirm } = useConfirm({
            title: 'Delete Child Category?',
            text: 'Are you sure you want to delete this child category?',
            confirmButtonText: 'Yes, delete child category!',
            cancelButtonText: 'Cancel'
        });

  const [handledeleteDataApi, { isLoading: isDeleteLoading }] =
    useDeleteChildCategoriesMutation();
  const onDeleteDataHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
      if (confirmed) {
        try {
          const body = { id: id };
          const resp = await handledeleteDataApi(body).unwrap();
          handleApiSuccess(resp);
        } catch (error) {
          handleApiErrors(error);
        }
    }
  };
  /* **************** End Delete Child Categories ******************* */
  /* **************** Start Edit Child Categories Model ******************* */
  const [editValues, setEditValues] = useState({
    id: "",
    category_id: "",
    sub_category_id: "",
    title: "",
    title_ar: "",
    is_publish: "",
  });
  const handleOpenModal = (values) => {
    setEditValues(values);
    handleEditModelShow();
  };

  const onEditDataDetailsHandler = (d) => {
    setSelectedEditCategoryId(d.category_id);
    handleOpenModal({
      id: d?.id || "",
      category_id: d?.category_id || "",
      sub_category_id: d?.sub_category_id || "",
      title: d?.title || "",
      title_ar: d?.title_ar || "",
      is_publish: d?.is_publish || "",
    });
  };

  /* **************** End  Edit  Child Categories Model ******************* */
  /* **************** Start  Edit  Child Categories  ******************* */
  const [handleEditDataApi, { isLoading: isEditLoading }] =
    useEditChildCategoriesMutation();
  const DataUpdateFormSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        category_id: parseInt(body.category_id),
        sub_category_id: parseInt(body.sub_category_id),
        id: parseInt(body.id),
        is_publish: parseInt(body.is_publish),
      };
      const resp = await handleEditDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleEditModelClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Child Categories ******************* */

  /* **************** Start Fetch Subcategories Based On Category (Load In Select Box in Add Modal) ******************* */
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);
  const { data: subCategoriesData } = useGetAllSubCategoriesQuery(
    { category_id: selectedCategoryId },
    { skip: !selectedCategoryId }
  );
  const subCategoriesList = useMemo(() => {
    if (!subCategoriesData?.data?.length) {
      return [];
    }
    return subCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [subCategoriesData?.data]);
  /* **************** End Fetch Subcategories Based On Category (Load In Select Box in Add Modal) ******************* */
  /* **************** Start Handle category selection change in Add Modal ******************* */
  const handleCategoryChange = (e) => {
    setSelectedCategoryId(parseInt(e.target.value));
  };
  /* **************** End Handle category selection change in Add Modal ******************* */
  /* **************** Start Fetch Subcategories Based On Category (Load In Select Box in Edit Modal) ******************* */
  const [selectedEditCategoryId, setSelectedEditCategoryId] = useState(null);
  const { data: subEditCategoriesData } = useGetAllSubCategoriesQuery(
    { category_id: selectedEditCategoryId },
    { skip: !selectedEditCategoryId }
  );
  const subEditCategoriesList = useMemo(() => {
    if (!subEditCategoriesData?.data?.length) {
      return [];
    }
    return subEditCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [subEditCategoriesData?.data]);
  /* **************** End Fetch Subcategories Based On Category (Load In Select Box in Edit Modal) ******************* */
    /* **************** Start Handle category selection change in Edit Modal ******************* */
    const handleEditCategoryChange = (e) => {
      setSelectedEditCategoryId(parseInt(e.target.value));
    };
    /* **************** End  Handle category selection change in Edit Modal ******************* */
    /* **************** Start Fetch Subcategories Based On Category (Load in Filter Select Box) ******************* */
    const [selectedFilterCategoryId, setSelectedFilterCategoryId] = useState(null);
    const { data: subCategoriesFilterData } = useGetAllSubCategoriesQuery(
      { category_id: selectedFilterCategoryId },
      { skip: !selectedFilterCategoryId }
    );
    const subCategoriesFilterList = useMemo(() => {
      if (!subCategoriesFilterData?.data?.length) {
        return [];
      }
      return subCategoriesFilterData;
    }, [subCategoriesFilterData?.data]);
    /* **************** End Fetch Subcategories Based On Category (Load in Filter Select Box) ******************* */
    /* **************** Start Handle category selection change in Filter Select Box ******************* */
    const handleFilterCategoryChange = (e) => {
      const value = e.target.value === "" ? null : parseInt(e.target.value);
      setSelectedFilterCategoryId(value);
    };
    /* **************** End  Handle category selection change in Filter Select Box ******************* */

  /* **************** Web Loader  ******************* */
  if (isLoading || isCreateLoading || isDeleteLoading || isEditLoading)
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">Child Category List</h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <button
                          type="button"
                          className="btn btn-primary"
                          onClick={handleAddModelShow}
                        >
                          Create Child Category
                        </button>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                  <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex  gap-6">
                      <div>
                        <select
                          value={filterCategoryId}
                          className="form-control search-chat py-2 "
                          onChange={handleCategoryFilter}
                        >
                          <option value="">All Categories</option>
                          {categoriesList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterSubCategoryId}
                          className="form-control search-chat py-2 "
                          onChange={handleSubCategoryFilter}
                        >
                          <option value="">All Sub Categories</option>
                          {subCategoriesFilterData?.data?.map((option) => (
                            <option key={option.id} value={option.id}>
                              {option.title}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterStatus}
                          className="form-control search-chat py-2 "
                          onChange={handleStatusFilter}
                        >
                          <option value="">All Status</option>
                          {generalStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "main_category_name",
                        label: "Category Name",
                        align: "left",
                      },
                      {
                        key: "sub_category_name",
                        label: "Sub Category Name",
                        align: "left",
                      },
                      {
                        key: "title",
                        label: "Title",
                        align: "left",
                      },
                      {
                        key: "title_ar",
                        label: "Title in Arabic",
                        align: "left",
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "left",
                      },
                      {
                        key: "created_at",
                        label: "Created At",
                        align: "left",
                      },
                    ]}
                    data={childCategoriesList}
                    onDeleteHandler={onDeleteDataHandler}
                    onEditHandler={onEditDataDetailsHandler}
                  />
                  <PaginationComponent
                    totalCount={pageData?.total_count}
                    pageSize={pageData?.page_size}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    onPageChange={fetchData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <!-- Create Modal --> */}

      <Modal
        show={showAddModel}
        onHide={handleAddModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title>Create Child Category </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={initialValues}
            validationSchema={validation}
            onSubmit={handleCreateFormSubmitFunction}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-create"
                className="needs-validation"
                autoComplete="off"
              >
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="category_id" className="form-label">
                          Category
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="category_id"
                          id="category_id"
                          className="form-select"
                          type="select"
                          options={categoriesList}
                          onChange={handleCategoryChange}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="sub_category_id" className="form-label">
                          Sub Category
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="sub_category_id"
                          id="sub_category_id"
                          className="form-select"
                          type="select"
                          options={subCategoriesList}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title" className="form-label">
                          Sub Category Name in English{" "}
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title"
                          id="title"
                          placeholder="Sub Category Name in English "
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title_ar" className="form-label">
                          Sub Category Name in Arabic{" "}
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title_ar"
                          id="title_ar"
                          placeholder="Sub Category Name in Arabic "
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="is_publish" className="form-label">
                          Status
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="is_publish"
                          id="is_publish"
                          className="form-select"
                          type="select"
                          options={generalStatusList}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                    data-bs-dismiss="modal"
                    onClick={handleAddModelClose}
                  >
                    Close
                  </button>
                  <button
                    className="btn btn-primary"
                    type="submit"
                    onClick={handleSubmit}
                  >
                    Create Child Category
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
      </Modal>
      {/* <!-- Create Modal end --> */}

      {/* <!-- EditModal --> */}

      <Modal
        show={showEditModel}
        onHide={handleEditModelClose}
        backdrop="static"
        keyboard={false}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <Modal.Header closeButton>
          <Modal.Title> Update Child Category</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            initialValues={editValues}
            enableReinitialize
            validationSchema={validation}
            onSubmit={DataUpdateFormSubmit}
          >
            {({ handleSubmit }) => (
              <Form
                name="role-update"
                className="needs-validation"
                autoComplete="off"
              >
                <FormikField
                  type="hidden"
                  name="role_id"
                  id="role_id"
                  autoComplete="off"
                  className="form-control"
                />
                <div className="modal-body">
                  <div className="row">
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="category_id" className="form-label">
                          Category
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="category_id"
                          id="category_id"
                          className="form-select"
                          type="select"
                          options={categoriesList}
                          onChange={handleEditCategoryChange}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="sub_category_id" className="form-label">
                          Sub Category
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="sub_category_id"
                          id="sub_category_id"
                          className="form-select"
                          type="select"
                          options={subEditCategoriesList}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title" className="form-label">
                          Category Name in English
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title"
                          id="title"
                          placeholder="Category Name  in English "
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="title_ar" className="form-label">
                          Category Name in Arabic
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          type="text"
                          name="title_ar"
                          id="title_ar"
                          placeholder="Category Name  in Arabic "
                          autoComplete="off"
                          className="form-control"
                        />
                      </div>
                    </div>
                    <div className="col-lg-12">
                      <div className="mb-3">
                        <label htmlFor="is_publish" className="form-label">
                          Status
                          <span className="un-validation">(*)</span> :
                        </label>
                        <FormikField
                          name="is_publish"
                          id="is_publish"
                          className="form-select"
                          type="select"
                          options={generalStatusList}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                    data-bs-dismiss="modal"
                    onClick={handleEditModelClose}
                  >
                    Close
                  </button>
                  <button className="btn btn-primary" onClick={handleSubmit}>
                    Update Child Category
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </Modal.Body>
      </Modal>
      {/* <!-- EditModal end --> */}
    </>
  );
}
