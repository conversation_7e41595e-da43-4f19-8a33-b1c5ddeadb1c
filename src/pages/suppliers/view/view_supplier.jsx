import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { useEffect, useMemo, useState } from "react";
import { Table } from "../../../components/datatable";
import { PaginationComponent } from "../../../components/pagination";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../components/formikField";
import { useEditSupplierMutation, useSingleSupplierQuery } from "../../../feature/api/supplierDataApiSlice";
import { Link, useNavigate, useParams } from "react-router-dom";
import { Modal } from "react-bootstrap";
import WebLoader from "../../../components/webLoader";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import {
  useCreateSupplierContactsMutation,
  useDeleteSupplierContactsMutation,
  useEditSupplierContactsMutation,
  useGetSupplierContactsListQuery,
} from "../../../feature/api/supplierContactsDataApiSlice";
import useConfirm from "../../../hooks/useConfirm";
import {
  useCreateSupplierNotesMutation,
  useDeleteSupplierNotesMutation,
  useEditSupplierNotesMutation,
  useGetSupplierNotesListQuery,
} from "../../../feature/api/supplierNotesDataApiSlice";
import {
  useCreateSupplierAttachmentsMutation,
  useDeleteSupplierAttachmentsMutation,
  useGetSupplierAttachmentsListQuery,
} from "../../../feature/api/supplierAttachmentsDataApiSlice";
import { useSelector } from "react-redux";
import { handleCustomError } from "../../../hooks/handleCustomError";
import { useDeleteSupplierInvoiceMutation, useListSupplierInvoiceQuery } from "../../../feature/api/supplierInvoiceDataApiSlice";

const validation = yup.object().shape({
  supplier_name: yup.string().required().label("Supplier Name"),
  phone_code: yup.string().required().label("Phone Code"),
  phone: yup.string().required().label("Phone"),
  email: yup.string().email().required().label("Email"),
  billing_address: yup.string().label("Billing Address"),
  tax_id: yup.string().label("Tax ID"),
  payment_terms: yup.string().label("Payment Terms"),
  credit_limit: yup.string().label("Credit Limit"),
  status: yup.string().required().label("Status"),
  remarks: yup.string().label("Remarks"),
  branch_id: yup.string().required().label("Branch"),
});
export default function ViewSupplier() {
  const navigation = useNavigate();
  const { id } = useParams();
  const supplierId = parseInt(id);
  const activePage = "Suppliers Master";
  const linkHref = "/dashboard";

  /* **************** Start fetching single supplier data ******************* */
  let { data: singleSupplier, isLoading: isSupplierLoading } = useSingleSupplierQuery({
      supplier_id: supplierId,
  });

  const supplierData = useMemo(() => {
    return singleSupplier?.data || null;
  }, [singleSupplier]);
  /* **************** Start fetching single supplier data ******************* */

    const [initialValues, setFormValues] = useState({
    supplier_name: supplierData?.supplier_name || null,
    phone_code: supplierData?.phone_code || "",
    phone: supplierData?.phone || "",
    email: supplierData?.email || "",
    country: supplierData?.country_iso3 || "",
    currency_code: supplierData?.currency_code || "",
    billing_address: supplierData?.billing_address || "",
    tax_id: supplierData?.tax_id || "",
    payment_terms: supplierData?.payment_terms || "",
    credit_limit: supplierData?.credit_limit || "",
    status: supplierData?.status || "",
    remarks: supplierData?.remarks || "",
    branch_id: supplierData?.branch_id || "",
    supplier_code: supplierData?.supplier_code || "",
  });

  useEffect(() => {
    if (isSupplierLoading) return;

    if (!supplierData) {
      handleCustomError("Error was found, please try again later!");
        navigation("/suppliers");
      } else {
        setFormValues({
          supplier_name: supplierData.supplier_name || "",
          phone_code: supplierData.phone_code || "",
          phone: supplierData.phone || "",
          email: supplierData.email || "",
          country: supplierData.country_iso3 || "",
          currency_code: supplierData.currency_code || "",
          billing_address: supplierData.billing_address || "",
          tax_id: supplierData.tax_id || "",
          payment_terms: supplierData.payment_terms || "",
          credit_limit: supplierData.credit_limit || "",
          status: supplierData.status || "",
          remarks: supplierData.remarks || "",
          branch_id: supplierData.branch_id || "",
          supplier_code: supplierData.supplier_code || "",
      });
    }
  }, [isSupplierLoading, supplierData, navigation]);

  /* **************** Start list all countries ******************* */

  const countries = useSelector((state) => state.commonState.countries);
  const countriesList = countries?.data
    ? countries.data.map((values) => ({
        value: values.phone_code,
        label: values.name + " (" + values.phone_code + ")",
      }))
    : [];

    //  filtering out unique currency code

    const uniqueCurrencies = [
      ...new Set(
        countries.data.map((values) => values.currency_code)
      )
    ].map((currency_code) => {
      const values = countries.data.find((v) => v.currency_code === currency_code);
      return {
        value: values.currency_code,
        label: `${values.currency_name} (${values.currency_code})`,
      };
    });
  /* **************** End list all countries ******************* */

  /* **************** Start list User Status ******************* */
  const userStatusData = useSelector((state) => state.commonState.userStatus);
  const userStatusDataList = userStatusData?.data || [];
  const userStatusList = userStatusDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list User Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Edit Supplier ******************* */
  const [handleEditSupplierApi, { isLoading: isEditSupplierLoading }] =
    useEditSupplierMutation();
  const handleSubmit = async (body) => {
    try {
      const createBody = {
        ...body,
        supplier_id: parseInt(supplierId),
        branch_id: parseInt(body.branch_id),
        status: parseInt(body.status),
      };
      const resp = await handleEditSupplierApi(createBody).unwrap();
      setFormValues({ ...body });
      handleApiSuccess(resp);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Edit Supplier ******************* */

  /* **************** Start Supplier Contacts Section ************** */

  const contactValidation = yup.object().shape({
    contact_name: yup.string().required().label("Contact Name"),
    phone_code: yup.string().required().label("Phone Code"),
    phone: yup.string().required().label("Phone"),
    email: yup.string().email().label("Email"),
    remarks: yup.string().label("Remarks"),
  });

  /* **************** Start Contact Data Add Modal ******************* */
  const [showAddContactModal, setShowAddContactModal] = useState(false);
  const handleAddContactModalClose = () => setShowAddContactModal(false);
  const handleAddContactModalShow = () => setShowAddContactModal(true);
  /* **************** End Start Contact Data Add Modal ******************* */

  /* **************** Start Contact Data Edit Modal ******************* */
  const [showEditContactModal, setShowEditContactModal] = useState(false);
  const handleContactEditModalClose = () => setShowEditContactModal(false);
  const handleContactEditModalShow = () => setShowEditContactModal(true);
  /* **************** End Contact Data Edit Modal ******************* */

  /* **************** Start list all Supplier Contact ******************* */

  const [currentContactPage, setCurrentContactPage] = useState(1);
  const [filterContactsTableKeywords, setFilterContactKeywords] = useState("");

  /* **************** Start list all Supplier Contacts ******************* */

  const { data: supplierContactsData, isLoading: isLoadingContacts } =
    useGetSupplierContactsListQuery({
      page: currentContactPage,
      supplier_id: parseInt(supplierId),
      keywords: filterContactsTableKeywords,
    });
  const supplierContactsList = useMemo(() => {
    if (!supplierContactsData?.data.list?.length) {
      if (currentContactPage > 1)
        setCurrentContactPage((current) => current - 1);
      return [];
    }
    return supplierContactsData?.data?.list;
  }, [currentContactPage, supplierContactsData?.data.list]);

  const contactPageData = useMemo(
    () => supplierContactsData?.data?.page ?? null,
    [supplierContactsData]
  );

  /* **************** End list all Supplier Contacts ******************* */

  /* **************** Start Filter ***************** */
  const handleContactKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterContactKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchContactData = async (page) => {
    try {
      setCurrentContactPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start Delete Supplier Contact ******************* */

  const { showSimpleConfirm: showSimpleContactConfirm } = useConfirm({
    title: "Delete Supplier Contact?",
    text: "Are you sure you want to delete this supplier contact?",
    confirmButtonText: "Yes, delete supplier contact!",
    cancelButtonText: "Cancel",
  });

  const [handleDeleteContactDataApi, { isLoading: isDeleteContactLoading }] =
    useDeleteSupplierContactsMutation();
  const onDeleteContactDataHandler = async (id) => {
    const confirmed = await showSimpleContactConfirm();
    if (confirmed) {
      try {
        const body = { contact_id: id };
        const resp = await handleDeleteContactDataApi(body).unwrap();
        handleApiSuccess(resp);
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };

  /* **************** End Delete Supplier Contact ******************* */

  /* **************** Start Create Supplier Contact ******************* */
  const [handleCreateContactDataApi, { isLoading: isCreateContactLoading }] =
    useCreateSupplierContactsMutation();
  const handleCreateContactFormSubmitFunction = async (body) => {
    try {
      const updatedBody = {
        ...body,
        supplier_id: parseInt(supplierId),
      };
      const resp = await handleCreateContactDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleAddContactModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create  Supplier Contact ******************* */

  const [editContactValues, setEditContactValues] = useState({
    supplier_id: "",
    contact_name: "",
    phone_code: "",
    phone: "",
    email: "",
    remarks: "",
  });
  const handleOpenModal = (values) => {
    setEditContactValues(values);
    handleContactEditModalShow();
  };

  const onEditContactDataDetailsHandler = (d) => {
    handleOpenModal({
      supplier_id: d?.supplier_id ? parseInt(d.supplier_id) : "",
      id: d?.id || "",
      contact_name: d?.contact_name || "",
      phone_code: d?.phone_code || "",
      phone: d?.phone || "",
      email: d?.email || "",
      remarks: d?.remarks || "",
    });
  };

  const [handleEditContactDataApi, { isLoading: isEditContactLoading }] =
    useEditSupplierContactsMutation();
  const contactDataUpdateFormSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        contact_id: parseInt(body.id),
        supplier_id: parseInt(body.supplier_id),
      };
      const resp = await handleEditContactDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleContactEditModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* *************** End Supplier Contacts Section **************** */

  /* *************** Start Supplier Notes Section **************** */

  const notesValidation = yup.object().shape({
    remarks: yup.string().required().label("Remarks"),
  });

  /* **************** Start Notes Data Add Modal ******************* */
  const [showAddNotesModal, setShowAddNotesModal] = useState(false);
  const handleAddNotesModalClose = () => setShowAddNotesModal(false);
  const handleAddNotesModalShow = () => setShowAddNotesModal(true);
  /* **************** End Notes Data Add Modal ******************* */

  /* **************** Notes Data Edit Modal ******************* */
  const [showEditNotesModal, setShowEditNotesModal] = useState(false);
  const handleEditNotesModalClose = () => setShowEditNotesModal(false);
  const handleEditNotesModalShow = () => setShowEditNotesModal(true);
  /* **************** End Notes Data Edit Modal ******************* */

  const [currentNotesPage, setCurrentNotesPage] = useState(1);

  const [filterNotesKeywords, setFilterNotesKeywords] = useState("");
  /* **************** Start list all Supplier Notes ******************* */
  const { data: supplierNotesData, isLoading: isNotesLoading } =
    useGetSupplierNotesListQuery({
      page: currentNotesPage,
      supplier_id: parseInt(supplierId),
      keywords: filterNotesKeywords,
    });
  const supplierNotesList = useMemo(() => {
    if (!supplierNotesData?.data.list?.length) {
      if (currentNotesPage > 1) setCurrentNotesPage((current) => current - 1);
      return [];
    }
    return supplierNotesData?.data?.list;
  }, [currentNotesPage, supplierNotesData?.data.list]);

  const notesPageData = useMemo(
    () => supplierNotesData?.data?.page ?? null,
    [supplierNotesData]
  );

  /* ****************  Start Filter ****************** */
  const handleNotesKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterNotesKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchNotesData = async (page) => {
    try {
      setCurrentNotesPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start Delete Supplier Notes ******************* */

  const { showSimpleConfirm: showSimpleConfirmNotes } = useConfirm({
    title: "Delete Supplier Notes?",
    text: "Are you sure you want to delete this supplier notes?",
    confirmButtonText: "Yes, delete supplier notes!",
    cancelButtonText: "Cancel",
  });

  const [handleDeleteNotesDataApi, { isLoading: isDeleteNotesLoading }] =
    useDeleteSupplierNotesMutation();
  const onDeleteNotesDataHandler = async (id) => {
    const confirmed = await showSimpleConfirmNotes();
    if (confirmed) {
      try {
        const body = { note_id: id };
        const resp = await handleDeleteNotesDataApi(body).unwrap();
        handleApiSuccess(resp);
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };
  /* **************** End Delete Supplier Notes ******************* */

  /* **************** Start Create Supplier Notes ******************* */
  const [handleCreateNotesDataApi, { isLoading: isCreateNotesLoading }] =
    useCreateSupplierNotesMutation();
  const handleCreateNotesFormSubmitFunction = async (body) => {
    try {
      const updatedBody = {
        ...body,
        supplier_id: parseInt(supplierId),
      };
      const resp = await handleCreateNotesDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleAddNotesModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create  Supplier Notes ******************* */

  const notesInitialValues = {
    supplier_id: supplierId,
    remarks: "",
  };

  const [editNotesValues, setEditNotesValues] = useState({
    supplier_id: "",
    remarks: "",
  });

  const handleOpenNotesModal = (values) => {
    setEditNotesValues(values);
    handleEditNotesModalShow();
  };
  const onEditNotesDataDetailsHandler = (d) => {
    handleOpenNotesModal({
      supplier_id: d?.supplier_id ? parseInt(d.supplier_id) : "",
      id: d?.id || "",
      remarks: d?.note_text || "",
    });
  };

  const [handleEditNotesDataApi, { isLoading: isEditNotesLoading }] =
    useEditSupplierNotesMutation();
  const notesDataUpdateFormSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        note_id: parseInt(body.id),
        supplier_id: parseInt(body.supplier_id),
      };
      const resp = await handleEditNotesDataApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      handleEditNotesModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* *************** End Supplier Notes Section **************** */

  /* *************** Start Supplier Attachment Section **************** */

  const attachmentInitialValues = {
    supplier_id: supplierId,
    attachment: "",
  };

  const attachmentValidation = yup.object().shape({
    attachment: yup.mixed().required().label("Attachment"),
  });

  /* **************** Data Add Modal ******************* */
  const [showAddAttachmentModal, setShowAddAttachmentModal] = useState(false);
  const handleAddAttachmentModalClose = () => setShowAddAttachmentModal(false);
  const handleAddAttachmentModalShow = () => setShowAddAttachmentModal(true);
  /* **************** End Data Add Modal ******************* */

  /* **************** Start list all Supplier Contact ******************* */
  const [currentAttachmentPage, setCurrentAttachmentPage] = useState(1);
  const [filterAttachmentKeywords, setFilterAttachmentKeywords] = useState("");
  /* **************** Start list all Supplier Attachment ******************* */
  const { data: supplierAttachmentData, isAttachmentsLoading } =
    useGetSupplierAttachmentsListQuery({
      page: currentAttachmentPage,
      supplier_id: parseInt(supplierId),
      keywords: filterAttachmentKeywords,
    });
  const supplierAttachmentList = useMemo(() => {
    if (!supplierAttachmentData?.data.list?.length) {
      if (currentAttachmentPage > 1)
        setCurrentAttachmentPage((current) => current - 1);
      return [];
    }
    return supplierAttachmentData?.data?.list;
  }, [currentAttachmentPage, supplierAttachmentData?.data.list]);

  const attachmentPageData = useMemo(
    () => supplierAttachmentData?.data?.page ?? null,
    [supplierAttachmentData]
  );

  /* **************** End list all Supplier Attachment ******************* */

  /* ****************  Start Filter ****************** */
  const handleAttachmentKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterAttachmentKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Filter ***************** */
  const fetchAttachmentData = async (page) => {
    try {
      setCurrentAttachmentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start Create Supplier Attachments ******************* */
  const [
    handleCreateAttachmentDataApi,
    { isLoading: isCreateAttachmentLoading },
  ] = useCreateSupplierAttachmentsMutation();

  const handleCreateAttachmentFormSubmitFunction = async (body) => {
    try {
      const formData = new FormData();
      for (const [key, value] of Object.entries(body)) {
        if (key === "attachment" && value) {
          formData.append(key, value);
        } else if (key === "supplier_id") {
          formData.append(key, parseInt(supplierId));
        } else {
          formData.append(key, value);
        }
      }
      const resp = await handleCreateAttachmentDataApi(formData).unwrap();
      handleApiSuccess(resp);
      handleAddAttachmentModalClose();
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create  Supplier Attachments ******************* */

  /* **************** Start Delete Supplier Attachments ******************* */

  const { showSimpleConfirm: showSimpleAttachmentConfirm } = useConfirm({
    title: "Delete Supplier Attachment?",
    text: "Are you sure you want to delete this supplier attachment?",
    confirmButtonText: "Yes, delete supplier attachment!",
    cancelButtonText: "Cancel",
  });

  const [
    handleDeleteAttachmentDataApi,
    { isLoading: isDeleteAttachmentLoading },
  ] = useDeleteSupplierAttachmentsMutation();
  const onDeleteAttachmentDataHandler = async (id) => {
    const confirmed = await showSimpleAttachmentConfirm();
    if (confirmed) {
      try {
        const body = { id: id };
        const resp = await handleDeleteAttachmentDataApi(body).unwrap();
        handleApiSuccess(resp);
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };
  /* **************** End Delete Supplier Attachments ******************* */

  /* *************** End Supplier Attachment Section **************** */

  /* *************** Start Supplier Invoice Section *************** */

    /* **************** Start list all invoice types ******************* */
    const invoiceTypes = useSelector((state) => state.commonState.invoiceTypes);
    /* **************** End list all invoice types ******************* */

  const [currentSupplierInvoicePage, setCurrentSupplierInvoicePage] = useState(1);
  const [filterSupplierInvoiceBranchId, setFilterSupplierInvoiceBranchId] = useState(null);
  const [filterSupplierInvoiceType, setFilterSupplierInvoiceType] = useState(null);
  const [filterSupplierInvoiceKeywords, setFilterSupplierInvoiceKeywords] = useState("");
  const [filterSupplierInvoiceDate, setFilterSupplierInvoiceDate] = useState("");

    /* ****************  Start Filter ****************** */
    const handleSupplierInvoiceBranchFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterSupplierInvoiceBranchId(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleSupplierInvoiceTypeFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterSupplierInvoiceType(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleSupplierInvoiceDateFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterSupplierInvoiceDate(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };
    const handleSupplierInvoiceKeywordsFilter = (event) => {
      try {
        const selectedValue = event.target.value;
        setFilterSupplierInvoiceKeywords(selectedValue);
      } catch (error) {
        handleApiErrors(error);
      }
    };

  const { data: paymentToSupplierInvoiceListResp, isSupplierInvoiceLoading } =
      useListSupplierInvoiceQuery({
        page: currentSupplierInvoicePage,
        supplier_id: parseInt(supplierId),
        branch_id: parseInt(filterSupplierInvoiceBranchId),
        invoice_type: parseInt(filterSupplierInvoiceType),
        date: filterSupplierInvoiceDate,
        keywords: filterSupplierInvoiceKeywords,
      });
    const paymentToSupplierInvoiceList = useMemo(() => {
      if (!paymentToSupplierInvoiceListResp?.data?.list?.length) {
        if (currentSupplierInvoicePage > 1) setCurrentSupplierInvoicePage((current) => current - 1);
        return [];
      }
      return paymentToSupplierInvoiceListResp?.data?.list;
    }, [currentSupplierInvoicePage, paymentToSupplierInvoiceListResp?.data?.list]);
    const supplierInvoicePageData = useMemo(
      () => paymentToSupplierInvoiceListResp?.data?.page ?? null,
      [paymentToSupplierInvoiceListResp]
    );

    /* **************** Start Paginatation ***************** */
    const fetchSupplierInvoiceData = async (page) => {
      try {
        setCurrentSupplierInvoicePage(page); // Update the page state
      } catch (error) {
        handleApiErrors(error);
      }
    };
    /* **************** End Paginatation ***************** */

    const { showSimpleConfirm } = useConfirm({
      title: "Delete Supplier Invoice?",
      text: "Are you sure you want to delete this supplier invoice?",
      confirmButtonText: "Yes, delete supplier invoice!",
      cancelButtonText: "Cancel",
    });
    const [handleDeleteSupplierInvoiceApi, { isLoading: isDeleteSupplierInvoiceLoading }] =
      useDeleteSupplierInvoiceMutation();
    const onDeleteSupplierInvoiceHandler = async (id) => {
      const confirmed = await showSimpleConfirm();
      if (confirmed) {
        try {
          const body = { id: id };
          const resp = await handleDeleteSupplierInvoiceApi(body).unwrap();
          handleApiSuccess(resp);
          navigation("/paymentToSupplierInvoice"); // Redirect to the desired page
        } catch (error) {
          handleApiErrors(error);
        }
      }
    };
  
    const onEditSupplierInvoiceDetailsHandler = (d) => {
      navigation("/editPaymentToSupplierInvoice", { state: d });
    };

  /* *************** End Supplier Invoice Section *************** */


  /* **************** Tab Management ******************* */
  const [activeTab, setActiveTab] = useState("supplier-details");

  const handleTabClick = (tabName) => {
    setActiveTab(tabName);
  };
  /* **************** End Tab Management ******************* */

  /* **************** Web Loader  ******************* */
  if (
    isEditSupplierLoading ||
    isLoadingContacts ||
    isEditContactLoading ||
    isDeleteContactLoading ||
    isEditNotesLoading ||
    isDeleteNotesLoading ||
    isNotesLoading ||
    isDeleteAttachmentLoading ||
    isAttachmentsLoading ||
    isCreateContactLoading ||
    isCreateNotesLoading ||
    isCreateAttachmentLoading ||
    isSupplierLoading || 
    isSupplierInvoiceLoading ||
    isDeleteSupplierInvoiceLoading
  )
    return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  ></div>
                  <ul
                    className="nav nav-pills user-profile-tab"
                    id="pills-tab"
                    role="tablist"
                  >
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "supplier-details" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("supplier-details")}
                      >
                        <i className="ti ti-user-circle me-2 fs-6"></i>
                        <span className="d-none d-md-block">
                          Supplier Details
                        </span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "contacts" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("contacts")}
                      >
                        <i className="ti ti-phone me-2 fs-6"></i>
                        <span className="d-none d-md-block">Contacts</span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "notes" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("notes")}
                      >
                        <i className="ti ti-note me-2 fs-6"></i>
                        <span className="d-none d-md-block">Notes</span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "attachments" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("attachments")}
                      >
                        <i className="ti ti-paperclip me-2 fs-6"></i>
                        <span className="d-none d-md-block">Attachments</span>
                      </button>
                    </li>
                    <li className="nav-item" role="presentation">
                      <button
                        className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                          activeTab === "supplier-invoice" ? "active" : ""
                        }`}
                        type="button"
                        onClick={() => handleTabClick("supplier-invoice")}
                      >
                        <i className="ti ti-file-invoice me-2 fs-6"></i>
                        <span className="d-none d-md-block">Supplier Invoice</span>
                      </button>
                    </li>
                  </ul>
                  <div className="card-body">
                    <div className="tab-content" id="pills-tabContent">
                      {/* Start Supplier Details Tab */}
                      {activeTab === "supplier-details" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-account"
                          role="tabpanel"
                          aria-labelledby="pills-account-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="card-body p-4">
                                  <h4 className="card-title">
                                    Supplier Details
                                  </h4>
                                  <p className="card-subtitle mb-4">
                                    To update Supplier, add details and save
                                    from here
                                  </p>
                                  <Formik
                                    initialValues={initialValues}
                                    validationSchema={validation}
                                    onSubmit={handleSubmit}
                                    enableReinitialize={true}
                                  >
                                    <Form
                                      name="supplier-edit"
                                      className="needs-validation"
                                      autoComplete="off"
                                      encType="multipart/form-data"
                                    >
                                      <div className="row">
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="branch_id"
                                              className="form-label"
                                            >
                                              Branch
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="branch_id"
                                              id="branch_id"
                                              className="form-select"
                                              type="select"
                                              options={branchesList}
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="supplier_code"
                                              className="form-label"
                                            >
                                              Supplier Code
                                            </label>
                                            <FormikField
                                              type="text"
                                              name="supplier_code"
                                              id="supplier_code"
                                              placeholder="Supplier Code *"
                                              autoComplete="off"
                                              disabled
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="supplier_name"
                                              className="form-label"
                                            >
                                              Supplier Name
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              type="text"
                                              name="supplier_name"
                                              id="supplier_name"
                                              placeholder="Supplier Name *"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="country"
                                              className="form-label"
                                            >
                                              Country
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="country"
                                              id="country"
                                              className="form-select"
                                              type="select"
                                              options={countries.data.map(
                                                (values) => ({
                                                  value: values.iso3,
                                                  label:
                                                    values.name +
                                                    " (" +
                                                    values.iso3 +
                                                    ")",
                                                })
                                              )}
                                            />
                                          </div>
                                        </div>                                        
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              className="form-label"
                                              htmlFor="phone"
                                            >
                                              Phone Number{" "}
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <div className="row">
                                              <div className="col-4">
                                                <FormikField
                                                  name="phone_code"
                                                  id="phone_code"
                                                  className="form-select"
                                                  type="select"
                                                  options={countriesList}
                                                />
                                              </div>
                                              <div className="col-8">
                                                <FormikField
                                                  type="number"
                                                  name="phone"
                                                  id="phone"
                                                  placeholder="Phone Number *"
                                                  autoComplete="off"
                                                  className="form-control"
                                                />
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="email"
                                              className="form-label"
                                            >
                                              Email
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              type="text"
                                              name="email"
                                              id="email"
                                              placeholder="Email"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="billing_address"
                                              className="form-label"
                                            >
                                              Billing Address
                                            </label>
                                            <FormikField
                                              type="textarea"
                                              name="billing_address"
                                              id="billing_address"
                                              placeholder="Billing Address"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6"></div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="payment_terms"
                                              className="form-label"
                                            >
                                              Payment Terms
                                            </label>
                                            <FormikField
                                              type="textarea"
                                              name="payment_terms"
                                              id="payment_terms"
                                              placeholder="Payment Terms"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="credit_limit"
                                              className="form-label"
                                            >
                                              Credit Limit
                                            </label>
                                            <FormikField
                                              type="number"
                                              name="credit_limit"
                                              id="credit_limit"
                                              placeholder="Credit Limit"
                                              autoComplete="off"
                                              className="form-control"
                                              step="0.01"
                                            />
                                          </div>
                                        </div>   
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="currency"
                                              className="form-label"
                                            >
                                              Currency
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="currency_code"
                                              id="currency_code"
                                              className="form-select"
                                              type="select"
                                              options={uniqueCurrencies}
                                            />
                                          </div>
                                        </div>                                     
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="tax_id"
                                              className="form-label"
                                            >
                                              Tax ID
                                            </label>
                                            <FormikField
                                              type="text"
                                              name="tax_id"
                                              id="tax_id"
                                              placeholder="Tax ID"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-6">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="status"
                                              className="form-label"
                                            >
                                              Status
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="status"
                                              id="status"
                                              className="form-select"
                                              type="select"
                                              options={userStatusList}
                                            />
                                          </div>
                                        </div>                                        
                                        <div className="col-lg-12">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="remarks"
                                              className="form-label"
                                            >
                                              Remarks
                                            </label>
                                            <FormikField
                                              type="textarea"
                                              name="remarks"
                                              id="remarks"
                                              placeholder="Remarks"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-12">
                                          <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                            <button
                                              className="btn btn-primary"
                                              type="submit"
                                            >
                                              Edit Supplier
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    </Form>
                                  </Formik>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      {/* End Supplier Details Tab */}

                      {/* Start Supplier Contacts Tab */}

                      {activeTab === "contacts" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-contacts"
                          role="tabpanel"
                          aria-labelledby="pills-contacts-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="px-4 py-3 border-bottom">
                                  <div className="d-sm-flex align-items-center justify-space-between">
                                    <h4 className="card-title mb-0">
                                      Supplier Contact List
                                    </h4>

                                    <nav
                                      aria-label="breadcrumb"
                                      className="ms-auto"
                                    >
                                      <ol className="breadcrumb">
                                        <li
                                          className="breadcrumb-item"
                                          aria-current="page"
                                        >
                                          <button
                                            type="button"
                                            className="btn btn-primary"
                                            onClick={handleAddContactModalShow}
                                          >
                                            Create Supplier Contact
                                          </button>
                                        </li>
                                      </ol>
                                    </nav>
                                  </div>
                                </div>
                                <div className="card-body">
                                  <div className="table-responsive">
                                    <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                      <div className="d-flex  gap-6"></div>
                                      <div className="position-relative">
                                        <input
                                          type="text"
                                          className="form-control search-chat py-2 ps-5"
                                          id="text-srh"
                                          onChange={handleContactKeywordsFilter}
                                          placeholder="Keyword Search..."
                                          value={filterContactsTableKeywords}
                                        />
                                        <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                      </div>
                                    </div>
                                    <Table
                                      headCells={[
                                        {
                                          key: "sel_id",
                                          label: "#",
                                          align: "left",
                                        },
                                        {
                                          key: "contact_name",
                                          label: "Contact Name",
                                          align: "left",
                                        },
                                        {
                                          key: "phone",
                                          label: "Phone",
                                          align: "left",
                                        },
                                        {
                                          key: "email",
                                          label: "Email",
                                          align: "left",
                                        },
                                        {
                                          key: "created_at",
                                          label: "Created At",
                                          align: "left",
                                        },
                                      ]}
                                      data={supplierContactsList}
                                      onDeleteHandler={
                                        onDeleteContactDataHandler
                                      }
                                      onEditHandler={
                                        onEditContactDataDetailsHandler
                                      }
                                    />
                                    <PaginationComponent
                                      totalCount={contactPageData?.total_count}
                                      pageSize={contactPageData?.page_size}
                                      currentPage={currentContactPage}
                                      setCurrentPage={setCurrentContactPage}
                                      onPageChange={fetchContactData}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Start Supplier Contacts Create Modal */}

                      {/* <!-- Create Modal --> */}

                      <Modal
                        show={showAddContactModal}
                        onHide={handleAddContactModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title>Create Supplier Contact </Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={editContactValues}
                            validationSchema={contactValidation}
                            onSubmit={handleCreateContactFormSubmitFunction}
                          >
                            {({ handleSubmit }) => (
                              <Form
                                name="role-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="modal-body">
                                  <div className="row">
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="contact_name"
                                          className="form-label"
                                        >
                                          Contact Name
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="contact_name"
                                          id="contact_name"
                                          placeholder="Contact Name *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label className="form-label">
                                          Phone Number{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <div className="row">
                                          <div className="col-4">
                                            <FormikField
                                              name="phone_code"
                                              id="phone_code"
                                              className="form-select"
                                              type="select"
                                              options={countriesList}
                                            />
                                          </div>
                                          <div className="col-8">
                                            <FormikField
                                              type="number"
                                              name="phone"
                                              id="phone"
                                              placeholder="Phone Number *"
                                              autoComplete="off"
                                              className="form-control"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="email"
                                          className="form-label"
                                        >
                                          Email
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="email"
                                          id="email"
                                          placeholder="Email"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="remarks"
                                          className="form-label"
                                        >
                                          Remarks
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="remarks"
                                          id="remarks"
                                          placeholder="Remarks"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="modal-footer">
                                  <button
                                    type="button"
                                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                                    data-bs-dismiss="modal"
                                    onClick={handleAddContactModalClose}
                                  >
                                    Close
                                  </button>
                                  <button
                                    className="btn btn-primary"
                                    type="submit"
                                    onClick={handleSubmit}
                                  >
                                    Create Supplier Contact
                                  </button>
                                </div>
                              </Form>
                            )}
                          </Formik>
                        </Modal.Body>
                      </Modal>

                      {/* <!-- Create Modal end --> */}

                      {/* End Supplier Contact Create Modal */}

                      {/* Start Supplier Contacts Edit Modal */}

                      <Modal
                        show={showEditContactModal}
                        onHide={handleContactEditModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title> Update Supplier Contact</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={editContactValues}
                            enableReinitialize
                            validationSchema={contactValidation}
                            onSubmit={contactDataUpdateFormSubmit}
                          >
                            {({ handleSubmit }) => {
                              return (
                                <Form
                                  name="role-update"
                                  className="needs-validation"
                                  autoComplete="off"
                                >
                                  <FormikField
                                    type="hidden"
                                    name="id"
                                    id="id"
                                    autoComplete="off"
                                    className="form-control"
                                  />
                                  <div className="modal-body">
                                    <div className="row">
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="contact_name"
                                            className="form-label"
                                          >
                                            Contact Name
                                            <span className="un-validation">
                                              (*)
                                            </span>
                                          </label>
                                          <FormikField
                                            type="text"
                                            name="contact_name"
                                            id="contact_name"
                                            placeholder="Contact Name"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="phone"
                                            className="form-label"
                                          >
                                            Phone Number{" "}
                                            <span className="un-validation">
                                              (*)
                                            </span>{" "}
                                            :
                                          </label>
                                          <div className="row">
                                            <div className="col-4">
                                              <FormikField
                                                name="phone_code"
                                                id="phone_code"
                                                className="form-select"
                                                type="select"
                                                options={countriesList}
                                              />
                                            </div>
                                            <div className="col-8">
                                              <FormikField
                                                type="number"
                                                name="phone"
                                                id="phone"
                                                placeholder="Phone Number"
                                                autoComplete="off"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="email"
                                            className="form-label"
                                          >
                                            Email
                                          </label>
                                          <FormikField
                                            type="email"
                                            name="email"
                                            id="email"
                                            placeholder="Email"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="remarks"
                                            className="form-label"
                                          >
                                            Remarks
                                          </label>
                                          <FormikField
                                            type="textarea"
                                            name="remarks"
                                            id="remarks"
                                            placeholder="Remarks"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="modal-footer">
                                    <button
                                      type="button"
                                      className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                                      data-bs-dismiss="modal"
                                      onClick={handleContactEditModalClose}
                                    >
                                      Close
                                    </button>
                                    <button
                                      className="btn btn-primary"
                                      onClick={handleSubmit}
                                    >
                                      Update Supplier Contact
                                    </button>
                                  </div>
                                </Form>
                              );
                            }}
                          </Formik>
                        </Modal.Body>
                        {/* <Modal.Footer></Modal.Footer> */}
                      </Modal>

                      {/* End Supplier Contacts Edit Modal */}

                      {/* End Supplier Contacts Tab */}

                      {/* Start Notes Tab */}

                      {activeTab === "notes" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-notes"
                          role="tabpanel"
                          aria-labelledby="pills-notes-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="px-4 py-3 border-bottom">
                                  <div className="d-sm-flex align-items-center justify-space-between">
                                    <h4 className="card-title mb-0">
                                      Supplier Notes List
                                    </h4>
                                    <nav
                                      aria-label="breadcrumb"
                                      className="ms-auto"
                                    >
                                      <ol className="breadcrumb">
                                        <li
                                          className="breadcrumb-item"
                                          aria-current="page"
                                        >
                                          <button
                                            type="button"
                                            className="btn btn-primary"
                                            onClick={handleAddNotesModalShow}
                                          >
                                            Create Supplier Notes
                                          </button>
                                        </li>
                                      </ol>
                                    </nav>
                                  </div>
                                </div>
                                <div className="card-body">
                                  <div className="table-responsive">
                                    <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                      <div className="d-flex  gap-6"></div>
                                      <div className="position-relative">
                                        <input
                                          type="text"
                                          className="form-control search-chat py-2 ps-5"
                                          id="text-srh"
                                          onChange={handleNotesKeywordsFilter}
                                          placeholder="Keyword Search..."
                                          value={filterNotesKeywords}
                                        />
                                        <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                      </div>
                                    </div>
                                    <Table
                                      headCells={[
                                        {
                                          key: "sel_id",
                                          label: "#",
                                          align: "left",
                                        },
                                        {
                                          key: "note_text",
                                          label: "Note",
                                          align: "left",
                                        },
                                        {
                                          key: "created_at",
                                          label: "Created At",
                                          align: "left",
                                        },
                                      ]}
                                      data={supplierNotesList}
                                      onDeleteHandler={onDeleteNotesDataHandler}
                                      onEditHandler={
                                        onEditNotesDataDetailsHandler
                                      }
                                    />
                                    <PaginationComponent
                                      totalCount={notesPageData?.total_count}
                                      pageSize={notesPageData?.page_size}
                                      currentPage={currentNotesPage}
                                      setCurrentPage={setCurrentNotesPage}
                                      onPageChange={fetchNotesData}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Start Notes Create Modal  */}

                      <Modal
                        show={showAddNotesModal}
                        onHide={handleAddNotesModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title>Create Supplier Notes </Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={notesInitialValues}
                            validationSchema={notesValidation}
                            onSubmit={handleCreateNotesFormSubmitFunction}
                          >
                            {({ handleSubmit }) => (
                              <Form
                                name="role-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="modal-body">
                                  <div className="row">
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="remarks"
                                          className="form-label"
                                        >
                                          Remarks
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="remarks"
                                          id="remarks"
                                          placeholder="Remarks *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="modal-footer">
                                  <button
                                    type="button"
                                    className="btn bg-danger-subtle text-danger  waves-effect text-start "
                                    data-bs-dismiss="modal"
                                    onClick={handleAddNotesModalClose}
                                  >
                                    Close
                                  </button>
                                  <button
                                    className="btn btn-primary"
                                    type="submit"
                                    onClick={handleSubmit}
                                  >
                                    Create Supplier Notes
                                  </button>
                                </div>
                              </Form>
                            )}
                          </Formik>
                        </Modal.Body>
                      </Modal>

                      {/* End Notes Create Modal */}

                      {/* <!-- Start Notes Edit Modal --> */}

                      <Modal
                        show={showEditNotesModal}
                        onHide={handleEditNotesModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title> Update Supplier Notes</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={editNotesValues}
                            enableReinitialize
                            validationSchema={notesValidation}
                            onSubmit={notesDataUpdateFormSubmit}
                          >
                            {({ handleSubmit }) => {
                              return (
                                <Form
                                  name="role-update"
                                  className="needs-validation"
                                  autoComplete="off"
                                >
                                  <FormikField
                                    type="hidden"
                                    name="id"
                                    id="id"
                                    autoComplete="off"
                                    className="form-control"
                                  />
                                  <div className="modal-body">
                                    <div className="row">
                                      <div className="col-lg-12">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="remarks"
                                            className="form-label"
                                          >
                                            Remarks
                                            <span className="un-validation">
                                              (*)
                                            </span>
                                          </label>
                                          <FormikField
                                            type="textarea"
                                            name="remarks"
                                            id="remarks"
                                            placeholder="Remarks *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="modal-footer">
                                    <button
                                      type="button"
                                      className="btn bg-danger-subtle text-danger  waves-effect text-start ClassModel"
                                      data-bs-dismiss="modal"
                                      onClick={handleEditNotesModalClose}
                                    >
                                      Close
                                    </button>
                                    <button
                                      className="btn btn-primary"
                                      onClick={handleSubmit}
                                    >
                                      Update Supplier Notes
                                    </button>
                                  </div>
                                </Form>
                              );
                            }}
                          </Formik>
                        </Modal.Body>
                      </Modal>

                      {/* <!-- End Notes Edit Modal end --> */}

                      {/* End Notes Tab */}

                      {/* Start Attachments Tab */}

                      {activeTab === "attachments" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-attachments"
                          role="tabpanel"
                          aria-labelledby="pills-attachments-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="px-4 py-3 border-bottom">
                                  <div className="d-sm-flex align-items-center justify-space-between">
                                    <h4 className="card-title mb-0">
                                      Supplier Attachments List
                                    </h4>
                                    <nav
                                      aria-label="breadcrumb"
                                      className="ms-auto"
                                    >
                                      <ol className="breadcrumb">
                                        <li
                                          className="breadcrumb-item"
                                          aria-current="page"
                                        >
                                          <button
                                            type="button"
                                            className="btn btn-primary"
                                            onClick={
                                              handleAddAttachmentModalShow
                                            }
                                          >
                                            Create Supplier Attachments
                                          </button>
                                        </li>
                                      </ol>
                                    </nav>
                                  </div>
                                </div>
                                <div className="card-body">
                                  <div className="table-responsive">
                                    <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                      <div className="d-flex gap-6"></div>
                                      <div className="position-relative">
                                        <input
                                          type="text"
                                          className="form-control search-chat py-2 ps-5"
                                          id="text-srh"
                                          onChange={
                                            handleAttachmentKeywordsFilter
                                          }
                                          placeholder="Keyword Search..."
                                          value={filterAttachmentKeywords}
                                        />
                                        <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                      </div>
                                    </div>
                                    <Table
                                      headCells={[
                                        {
                                          key: "sel_id",
                                          label: "#",
                                          align: "left",
                                        },
                                        {
                                          key: "file_name",
                                          label: "File Name",
                                          align: "left",
                                        },
                                        {
                                          key: "created_at",
                                          label: "Created At",
                                          align: "left",
                                        },
                                      ]}
                                      data={supplierAttachmentList}
                                      onDeleteHandler={
                                        onDeleteAttachmentDataHandler
                                      }
                                    />
                                    <PaginationComponent
                                      totalCount={
                                        attachmentPageData?.total_count
                                      }
                                      pageSize={attachmentPageData?.page_size}
                                      currentPage={currentAttachmentPage}
                                      setCurrentPage={setCurrentAttachmentPage}
                                      onPageChange={fetchAttachmentData}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* <!-- Start Create Attachment Modal --> */}

                      <Modal
                        show={showAddAttachmentModal}
                        onHide={handleAddAttachmentModalClose}
                        backdrop="static"
                        keyboard={false}
                        aria-labelledby="staticBackdropLabel"
                        aria-hidden="true"
                      >
                        <Modal.Header closeButton>
                          <Modal.Title>
                            Create Supplier Attachments{" "}
                          </Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                          <Formik
                            initialValues={attachmentInitialValues}
                            validationSchema={attachmentValidation}
                            onSubmit={handleCreateAttachmentFormSubmitFunction}
                          >
                            {({ handleSubmit }) => (
                              <Form
                                name="role-create"
                                className="needs-validation"
                                autoComplete="off"
                                encType="multipart/form-data"
                              >
                                <div className="modal-body">
                                  <div className="row">
                                    <div className="col-lg-12">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="attachment"
                                          className="form-label"
                                        >
                                          Attachment
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="file"
                                          name="attachment"
                                          id="attachment"
                                          placeholder="Attachment *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="modal-footer">
                                  <button
                                    type="button"
                                    className="btn bg-danger-subtle text-danger  waves-effect text-start"
                                    data-bs-dismiss="modal"
                                    onClick={handleAddAttachmentModalClose}
                                  >
                                    Close
                                  </button>
                                  <button
                                    className="btn btn-primary"
                                    type="submit"
                                    onClick={handleSubmit}
                                  >
                                    Create Supplier Attachments
                                  </button>
                                </div>
                              </Form>
                            )}
                          </Formik>
                        </Modal.Body>
                      </Modal>

                      {/* <!-- End Create Attachment Modal --> */}

                      {/* End Attachments Tab */}


                      {/* Start Supplier Invoice Tab */}

                      {activeTab === "supplier-invoice" && (
                        <div
                          className="tab-pane fade show active"
                          id="pills-supplier-invoice"
                          role="tabpanel"
                          aria-labelledby="pills-supplier-invoice-tab"
                          tabIndex="0"
                        >
                          <div className="row">
                            <div className="col-lg-12 d-flex align-items-stretch">
                              <div className="card w-100 border position-relative overflow-hidden mb-0">
                                <div className="px-4 py-3 border-bottom">
                                  <div className="d-sm-flex align-items-center justify-space-between">
                                    <h4 className="card-title mb-0">
                                      Supplier Invoice List
                                    </h4>
                                    <nav
                                      aria-label="breadcrumb"
                                      className="ms-auto"
                                    >
                                      <ol className="breadcrumb">
                                        <li className="breadcrumb-item" aria-current="page">
                                        <Link
                                          type="button"
                                          to={"/createPaymentToSupplierInvoice"}
                                          className="btn btn-primary"
                                        >
                                          Create New Payment To Supplier Invoice
                                        </Link>
                                      </li>
                                      </ol>
                                    </nav>
                                  </div>
                                </div>
                                <div className="card-body">
                                  <div className="table-responsive">
                                    <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                                      <div className="d-flex gap-6">
                                        <div>
                                          <select
                                            value={filterSupplierInvoiceBranchId}
                                            className="form-control search-chat py-2"
                                            onChange={handleSupplierInvoiceBranchFilter}
                                          >
                                            <option value="">All Branches</option>
                                            {branchesList.map((option) => (
                                              <option key={option.value} value={option.value}>
                                                {option.label}
                                              </option>
                                            ))}
                                          </select>
                                        </div>
                                        <div>
                                          <select
                                            value={filterSupplierInvoiceType}
                                            className="form-control search-chat py-2"
                                            onChange={handleSupplierInvoiceTypeFilter}
                                          >
                                            <option value="">All Invoice Types</option>
                                            {invoiceTypes?.data?.map((option) => (
                                              <option
                                                key={option.invoice_type_id}
                                                value={option.invoice_type_id}
                                              >
                                                {option.invoice_type}
                                              </option>
                                            ))}
                                          </select>
                                        </div>
                                        <div>
                                          <input
                                            type="date"
                                            className="form-control search-chat py-2"
                                            onChange={handleSupplierInvoiceDateFilter}
                                          />
                                        </div>
                                      </div>
                                      <div className="position-relative">
                                        <input
                                          type="text"
                                          className="form-control search-chat py-2 ps-5"
                                          id="text-srh"
                                          onChange={
                                            handleSupplierInvoiceKeywordsFilter
                                          }
                                          placeholder="Keyword Search..."
                                          value={filterSupplierInvoiceKeywords}
                                        />
                                        <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                                      </div>
                                    </div>
                                    <Table
                                      headCells={[
                                        { key: "sel_id", label: "#", align: "left" },
                                        {
                                          key: "supplier_invoice_number",
                                          label: "Invoice no.",
                                          align: "left",
                                          linkTo: (row) => ({
                                            to: "/editPaymentToSupplierInvoice",
                                            state: row,
                                          }),
                                        },
                                        {
                                          key: "invoice_type_name",
                                          label: "Invoice Type",
                                          align: "left",
                                        },
                                        {
                                          key: "branch_name",
                                          label: "Branch",
                                          align: "left",
                                          linkTo: (row) => ({
                                            to: `/editBranch/${row.branch_id}`,
                                          }),
                                        },
                                        {
                                          key: "exchange_rate_with_currency",
                                          label: "Exchange Rate",
                                          align: "center",
                                        },
                                        {
                                          key: "total_from_with_currency",
                                          label: "Total From",
                                          align: "center",
                                        },
                                        {
                                          key: "total_to_with_currency",
                                          label: "Total To",
                                          align: "center",
                                        },
                                        {
                                          key: "status_name",
                                          key_id: "status",
                                          label: "Status",
                                          align: "center",
                                        },
                                      ]}
                                      data={paymentToSupplierInvoiceList}
                                      onDeleteHandler={onDeleteSupplierInvoiceHandler}
                                      onEditHandler={onEditSupplierInvoiceDetailsHandler}
                                    />
                                    <PaginationComponent
                                      totalCount={
                                        supplierInvoicePageData?.total_count
                                      }
                                      pageSize={supplierInvoicePageData?.page_size}
                                      currentPage={currentSupplierInvoicePage}
                                      setCurrentPage={setCurrentSupplierInvoicePage}
                                      onPageChange={fetchSupplierInvoiceData}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* End Supplier Invoice Tab */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
