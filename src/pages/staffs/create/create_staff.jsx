import TopBar from "../../../components/layout/topBar";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useNavigate } from "react-router-dom";
import FormikField from "../../../components/formikField";
import Breadcrumb from "../../../components/breadcrumb";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useGetRolesQuery } from "../../../feature/api/rolesDataApiSlice";
import { useCreateStaffMutation } from "../../../feature/api/staffsDataApiSlice";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import WebLoader from "../../../components/webLoader";
import { useSelector } from "react-redux";

const initialValues = {
  branch_id: "",
  staff_role_id: "",
  name: "",
  email: "",
  phone_code: "",
  phone: "",
  qid: "",
  address: "",
  status: "",
  password: "",
  confirm_password: "",
};
const validation = yup.object().shape({
  branch_id: yup.string().required().label("Branch"),
  staff_role_id: yup.string().required().label("Employee Role"),
  name: yup.string().required().label("Employee Name"),
  email: yup.string().email().required().label("Email Address"),
  phone_code: yup.string().required().label("Phone Number"),
  phone: yup
    .string()
    .matches(/^\d{8}$/, "Phone number must be exactly 8 digits")
    .required()
    .label("Phone Number"),
  qid: yup.string().length(11).required().label("Qatar ID"),
  address: yup.string().required().label("Address"),
  status: yup.string().required().label("Status"),
  password: yup.string().min(8).required().label("Password"),
  confirm_password: yup
    .string()
    .oneOf(
      [yup.ref("password"), null],
      "Confirm password must be equal to new password"
    )
    .required()
    .label("Confirm Password"),
});
export default function CreateStaff() {
  const navigate = useNavigate(); // Initialize the navigation hook
  const activePage = "Create Employee";
  const linkHref = "/dashboard";

  /* **************** Start list all countries ******************* */

  const countries = useSelector((state) => state.commonState.countries);
  const countriesList = countries?.data
    ? countries.data.map((values) => ({
        value: values.phone_code,
        label: values.name + ' (' + values.phone_code + ')',
      }))
    : [];
  /* **************** End list all countries ******************* */

  /* **************** Start list all Branchs ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchListAry = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name,
  }));
  /* **************** End list all Branchs ******************* */

  /* **************** Start list all Staff Roles ******************* */
  let { data: rolesData } = useGetRolesQuery({
    page: 1,
  });
  const rolesDataList = rolesData?.data?.list || [];

  const rolesDataAry = rolesDataList.map((values) => ({
    value: values.id,
    label: values.role_name,
  }));
  /* **************** End list all Staff Roles ******************* */

  /* **************** Start list all Staff Status ******************* */
  const staffStatusData = useSelector((state) => state.commonState.staffStatus);
  const staffStatusDataRes = staffStatusData?.data || [];
  const staffStatusList = staffStatusDataRes.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list all Staff Status ******************* */

  /* **************** Start Create Staff  ******************* */
  const [handleCreateStaffApi, { isLoading: isLoading }] = useCreateStaffMutation();
  const handleSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        branch_id: parseInt(body.branch_id),
        staff_role_id: parseInt(body.staff_role_id),
        status: parseInt(body.status),
      };
      const resp = await handleCreateStaffApi(updatedBody).unwrap();
      handleApiSuccess(resp);
      navigate("/employees");
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Create Staff  ******************* */

  /* **************** Start Web Loader  ******************* */
            if (isLoading)
              return <WebLoader />;
  /* **************** End Web Loader  ******************* */
  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Employee Details</h4>
                            <p className="card-subtitle mb-4">
                              To create Employee, add details and save from here
                            </p>
                            <Formik
                              initialValues={initialValues}
                              validationSchema={validation}
                              onSubmit={handleSubmit}
                            >
                              <Form
                                name="branch-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_id"
                                        className="form-label"
                                      >
                                        Branch{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        name="branch_id"
                                        id="branch_id"
                                        className="form-select"
                                        type="select"
                                        options={branchListAry}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="staff_role_id"
                                        className="form-label"
                                      >
                                        Employee Role{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        name="staff_role_id"
                                        id="staff_role_id"
                                        className="form-select"
                                        type="select"
                                        options={rolesDataAry}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="name"
                                        className="form-label"
                                      >
                                        Employee Name{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="name"
                                        id="name"
                                        placeholder="Employee Name *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="email"
                                        className="form-label"
                                      >
                                        Employee Email Address{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        type="email"
                                        name="email"
                                        id="email"
                                        placeholder=" Employee Email Address *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label className="form-label">
                                        Phone Number{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <div className="row">
                                        <div className="col-4">
                                          <FormikField
                                            name="phone_code"
                                            id="phone_code"
                                            className="form-select"
                                            type="select"
                                            options={countriesList}
                                          />
                                        </div>

                                        <div className="col-8">
                                          <FormikField
                                            type="number"
                                            name="phone"
                                            id="phone"
                                            placeholder="Phone Number *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="qid"
                                        className="form-label"
                                      >
                                        Qatar ID{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        type="number"
                                        name="qid"
                                        id="qid"
                                        placeholder=" Qatar ID *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="address"
                                        className="form-label"
                                      >
                                        Address{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="address"
                                        id="address"
                                        placeholder=" Address *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="status"
                                        className="form-label"
                                      >
                                        Status
                                      </label>
                                      <FormikField
                                        name="status"
                                        id="status"
                                        className="form-select"
                                        type="select"
                                        options={staffStatusList}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="password"
                                        className="form-label"
                                      >
                                        Password{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        type="password"
                                        name="password"
                                        id="password"
                                        placeholder="Password *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>

                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="confirm_password"
                                        className="form-label"
                                      >
                                        Confirm Password{" "}
                                        <span className="un-validation">*</span>
                                      </label>
                                      <FormikField
                                        type="password"
                                        name="confirm_password"
                                        id="confirm_password"
                                        placeholder="  Confirm Password *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>

                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Create Employee
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
