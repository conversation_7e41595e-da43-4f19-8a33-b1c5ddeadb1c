import TopBar from "../../../../components/layout/topBar";
import Breadcrumb from "../../../../components/breadcrumb";
import { handleApiErrors } from "../../../../hooks/handleApiErrors";
import { handleCustomError } from "../../../../hooks/handleCustomError";
import { handleApiSuccess } from "../../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../../components/formikField";
import {
  useEditProductDetailMutation,
  useGetVariationsProductsMutation,
  useSingleProductDetailQuery,
} from "../../../../feature/api/productDataApiSlice";
import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useMemo, useState, useRef } from "react";
import WebLoader from "../../../../components/webLoader";
import { useSelector } from "react-redux";
import { useListAllAttributesQuery } from "../../../../feature/api/attributesDataApiSlice";
import { useGetAllAttributesValuesQuery } from "../../../../feature/api/attributesValuesDataApiSlice";
import Inventory from "../../../inventory/inventory";

export default function ViewProduct() {
  const navigate = useNavigate();
  const { id } = useParams();
  const productId = parseInt(id);
  const activePage = "Products Master";
  const linkHref = "/dashboard";
  const [selectedBranchId, setSelectedBranchId] = useState(null);
  const [detailsDescription, setDetailsDescription] = useState("");
  const [detailsDescriptionAr, setDetailsDescriptionAr] = useState("");
  const [editingProductDetailData, setEditingProductDetailData] =
    useState(null);
  const [productAttributes, setProductAttributes] = useState([]);
  const currentFormValuesRef = useRef({});

  /* Fetch single product data */
  const { data: singleProduct, isLoading: isProductLoading } =
    useSingleProductDetailQuery({ id: productId });
  const productData = useMemo(
    () => singleProduct?.data || null,
    [singleProduct]
  );

  /* Fetch product variations */
  const [getVariationsProducts] = useGetVariationsProductsMutation();
  const [singleProductVariations, setSingleProductVariations] = useState(null);
  const [isProductVariationLoading, setIsProductVariationLoading] =
    useState(false);

  useEffect(() => {
    if (singleProduct) {
      const fetchProductVariations = async () => {
        setIsProductVariationLoading(true);
        try {
          const response = await getVariationsProducts({
            product_id: singleProduct?.data?.product_id,
          }).unwrap();
          setSingleProductVariations(response);
        } catch (error) {
          console.error("Error fetching product variations:", error);
          setSingleProductVariations(null);
        } finally {
          setIsProductVariationLoading(false);
        }
      };
      fetchProductVariations();
    }
  }, [getVariationsProducts, singleProduct]);

  useEffect(() => {
    if (singleProductVariations && singleProduct) {
      setEditingProductDetailData(singleProduct?.data);
      setProductAttributes(singleProductVariations?.data || []);
    }
  }, [singleProductVariations, singleProduct]);

  const variationAttributeIds = useMemo(() => {
    if (!singleProductVariations?.data) return [];
    return singleProductVariations.data.map((variation) =>
      variation.attribute_id.toString()
    );
  }, [singleProductVariations]);

  /* Set product details */
  useEffect(() => {
    if (isProductLoading || isProductVariationLoading || !productData) return;
    if (!productData) {
      handleCustomError("Error was found, please try again later!");
      navigate("/products");
    } else {
      setProductAttributes(singleProductVariations?.data || []);
      setDetailsDescription(productData?.description || "");
      setDetailsDescriptionAr(productData?.description_ar || "");
      setSelectedBranchId(parseInt(productData?.branch_id));
    }
  }, [
    isProductLoading,
    productData,
    isProductVariationLoading,
    navigate,
    singleProductVariations,
  ]);

  /* General status */
  const generalStatuData = useSelector(
    (state) => state.commonState.generalStatus
  );
  const generalStatusList = useMemo(
    () =>
      (generalStatuData?.data || []).map((values) => ({
        value: values.id,
        label: values.status,
      })),
    [generalStatuData]
  );

  /* Attributes list */
  const { data: attributeData, isLoading: isAttributesLoading } =
    useListAllAttributesQuery();
  const attributes = useMemo(() => attributeData?.data || [], [attributeData]);
  const attributesMap = useMemo(() => {
    const map = {};
    attributes.forEach((attr) => {
      map[attr.id] = attr.title;
    });
    return map;
  }, [attributes]);

  /* Validation schema */
  const getProductDetailValidationSchema = (productAttributes) => {
    const baseValidation = {
      bar_code: yup.string().required().label("Barcode"),
      description: yup.string().label("Description"),
      description_ar: yup.string().label("Description Arabic"),
      is_publish: yup.string().required().label("Status"),
    };
    productAttributes.forEach((attr) => {
      baseValidation[`attribute_${attr.attribute_id}`] = yup
        .string()
        .required()
        .label(
          attributesMap[attr.attribute_id] || `Attribute ${attr.attribute_id}`
        );
    });
    return yup.object().shape(baseValidation);
  };

  /* Initial form values */
  const getProductDetailInitialValues = (
    productAttributes,
    currentValues = {},
    editData = null
  ) => {
    const baseValues = {
      product_id: singleProduct?.data?.product_id || "",
      bar_code: currentValues.bar_code || editData?.bar_code || "",
      description:
        currentValues.description ||
        editData?.description ||
        detailsDescription ||
        "",
      description_ar:
        currentValues.description_ar ||
        editData?.description_ar ||
        detailsDescriptionAr ||
        "",
      sku: currentValues.sku || editData?.sku || "",
      is_publish:
        currentValues.is_publish || editData?.is_publish?.toString() || "",
    };

    // Parse attributes from editData
    let attributesArray = [];
    if (editData?.attributes) {
      try {
        attributesArray = JSON.parse(editData.attributes) || [];
      } catch (e) {
        console.error("Error parsing attributes:", e);
        attributesArray = [];
      }
    }

    // Set attribute fields
    productAttributes.forEach((attr) => {
      const fieldName = `attribute_${attr.attribute_id}`;
      const attributeValue =
        attributesArray
          .find((attrId) => attrId === attr.id)
          ?.toString() || "";
      baseValues[fieldName] = currentValues[fieldName] || attributeValue || "";
    });

    return baseValues;
  };

  /* Attribute value select component */
  const AttributeValueSelect = ({
    attributeId,
    attributeTitle,
    formikProps,
  }) => {
    const {
      data: attributeValuesData,
      isLoading,
      error,
    } = useGetAllAttributesValuesQuery(
      { attribute_id: attributeId },
      { skip: !attributeId }
    );

    const attributeValueOptions = useMemo(() => {
      if (!attributeValuesData?.data?.length) return [];
      return attributeValuesData.data.map((value) => ({
        value: value.id.toString(),
        label: value.title,
      }));
    }, [attributeValuesData?.data]);

    // Set initial value when data is available
    useEffect(() => {
      if (attributeValuesData?.data?.length && formikProps) {
        const fieldName = `attribute_${attributeId}`;
        const currentValue = formikProps.values[fieldName];

        // Check if the current value is valid; if not, try to set from editingProductDetailData
        if (!currentValue && editingProductDetailData?.attributes) {
          let attributesArray = [];
          try {
            attributesArray =
              JSON.parse(editingProductDetailData.attributes) || [];
          } catch (e) {
            console.error("Error parsing attributes:", e);
          }
          const preSelectedValue = attributeValuesData.data.find((value) =>
            attributesArray.map(Number).includes(Number(value.id))
          );
          if (preSelectedValue) {
            formikProps.setFieldValue(
              fieldName,
              preSelectedValue.id.toString()
            );
          }
        }
      }
    }, [
      attributeValuesData?.data,
      attributeId,
      formikProps,
      // editingProductDetailData,
    ]);

    return (
      <div className="col-lg-6">
        <div className="mb-3">
          <label htmlFor={`attribute_${attributeId}`} className="form-label">
            {attributeTitle}
            <span className="un-validation">(*)</span>
            {isLoading && <small className="text-muted"> (Loading...)</small>}
          </label>
          {isLoading ? (
            <select className="form-select" disabled>
              <option>Loading...</option>
            </select>
          ) : error ? (
            <small className="text-danger">
              Error loading attribute values
            </small>
          ) : attributeValueOptions.length > 0 ? (
            <FormikField
              name={`attribute_${attributeId}`}
              id={`attribute_${attributeId}`}
              className="form-select"
              type="select"
              options={attributeValueOptions}
              defaultValue=""
            />
          ) : (
            <small className="text-muted">No attribute values available</small>
          )}
        </div>
      </div>
    );
  };

  /* Handle form submission */
  const [
    handleEditProductDetailApi,
    { isLoading: isEditProductDetailLoading },
  ] = useEditProductDetailMutation();
  const handleProductDetailsSubmit = async (body) => {
    try {
      const attributes = Object.keys(body)
        .filter((key) => key.startsWith("attribute_") && body[key])
        .map((key) => parseInt(body[key]));

      const productDetail = {
        id: editingProductDetailData?.id,
        branch_id: selectedBranchId,
        product_id: singleProduct?.data?.product_id,
        attributes: attributes.length > 0 ? JSON.stringify(attributes) : null,
        bar_code: body.bar_code,
        description: body.description || "",
        description_ar: body.description_ar || "",
        is_publish: parseInt(body.is_publish),
      };

      const resp = await handleEditProductDetailApi(productDetail).unwrap();
      handleApiSuccess(resp);
      navigate("/productDetails");
    } catch (error) {
      handleApiErrors(error);
    }
  };

  const [activeTab, setActiveTab] = useState("product-detail");

  const handleTabClick = (tabName) => {
    setActiveTab(tabName);
  };

  /* Web loader */
  if (
    isProductLoading ||
    isProductVariationLoading ||
    isAttributesLoading ||
    isEditProductDetailLoading
  ) {
    return <WebLoader />;
  }

  return (
    <div className="page-wrapper">
      <TopBar />
      <div className="body-wrapper">
        <div className="container-fluid ">
          <Breadcrumb activePage={activePage} linkHref={linkHref} />
          <div className="card">
            <div className="card-body">
              <div className="tab-content" id="pills-tabContent">
                <div
                  className="tab-pane fade show active"
                  id="pills-account"
                  role="tabpanel"
                  aria-labelledby="pills-account-tab"
                  tabIndex="0"
                ></div>
                <ul
                  className="nav nav-pills user-profile-tab"
                  id="pills-tab"
                  role="tablist"
                >
                  <li className="nav-item" role="presentation">
                    <button
                      className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                        activeTab === "product-detail" ? "active" : ""
                      }`}
                      type="button"
                      onClick={() => handleTabClick("product-detail")}
                    >
                      <i className="ti ti-package me-2 fs-6"></i>
                      <span className="d-none d-md-block">Product</span>
                    </button>
                  </li>
                  <li className="nav-item" role="presentation">
                    <button
                      className={`nav-link position-relative rounded-0 d-flex align-items-center justify-content-center bg-transparent fs-3 py-3 ${
                        activeTab === "inventory" ? "active" : ""
                      }`}
                      type="button"
                      onClick={() => handleTabClick("inventory")}
                    >
                      <i className="ti ti-server me-2 fs-6"></i>
                      <span className="d-none d-md-block">Inventory</span>
                    </button>
                  </li>
                </ul>
                <div className="card-body">
                  <div className="tab-content" id="pills-tabContent">
                    {/* Start Product Details Tab */}
                    {activeTab === "product-detail" && (
                      <div
                        className="tab-pane fade show active"
                        id="pills-product-detail"
                        role="tabpanel"
                        aria-labelledby="pills-product-detail-tab"
                        tabIndex="0"
                      >
                        <div className="row">
                          <div className="col-lg-12 d-flex align-items-stretch">
                            <div className="card w-100 border position-relative overflow-hidden mb-0">
                              <div className="card-body p-4">
                                <div className="d-flex justify-content-between align-items-center mb-4">
                                  <div>
                                    <h4 className="card-title">
                                      Edit Product Details
                                    </h4>
                                    <p className="card-subtitle mb-0">
                                      Update product details and save changes
                                    </p>
                                  </div>
                                </div>
                                <Formik
                                  initialValues={getProductDetailInitialValues(
                                    productAttributes,
                                    currentFormValuesRef.current,
                                    editingProductDetailData
                                  )}
                                  enableReinitialize={true}
                                  validationSchema={getProductDetailValidationSchema(
                                    productAttributes
                                  )}
                                  onSubmit={handleProductDetailsSubmit}
                                >
                                  {(formikProps) => {
                                    currentFormValuesRef.current =
                                      formikProps.values;
                                    return (
                                      <Form
                                        name="product-edit"
                                        className="needs-validation"
                                        autoComplete="off"
                                      >
                                        <div className="row">
                                          <hr />
                                          <h4 className="card-title">
                                            Product Details
                                          </h4>
                                          {productAttributes.map((attr) => (
                                            <AttributeValueSelect
                                              key={attr.attribute_id}
                                              attributeId={attr.attribute_id}
                                              attributeTitle={
                                                attributesMap[
                                                  attr.attribute_id
                                                ] ||
                                                `Attribute ${attr.attribute_id}`
                                              }
                                              formikProps={formikProps}
                                            />
                                          ))}
                                          <div className="row">
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="bar_code"
                                                className="form-label"
                                              >
                                                Barcode{" "}
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="text"
                                                name="bar_code"
                                                id="bar_code"
                                                placeholder="Enter barcode *"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="sku"
                                                className="form-label"
                                              >
                                                Sku{" "}
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="text"
                                                name="sku"
                                                id="sku"
                                                placeholder="Enter sku *"
                                                className="form-control"
                                                disabled={true}
                                              />
                                            </div>
                                          </div>                                          
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="description"
                                                className="form-label"
                                              >
                                                Description
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="description"
                                                id="description"
                                                placeholder="Enter description"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="description_ar"
                                                className="form-label"
                                              >
                                                Description Arabic
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="description_ar"
                                                id="description_ar"
                                                placeholder="Enter description arabic"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="is_publish"
                                                className="form-label"
                                              >
                                                Status{" "}
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="is_publish"
                                                id="is_publish"
                                                className="form-select"
                                                type="select"
                                                options={generalStatusList}
                                              />
                                            </div>
                                          </div>
                                          </div>
                                          <div className="col-12">
                                            <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                              <button
                                                className="btn btn-primary"
                                                type="submit"
                                              >
                                                Update Product Detail
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      </Form>
                                    );
                                  }}
                                </Formik>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab === "inventory" && (
                      <div className="row">
                        <div className="col-lg-12 d-flex align-items-stretch">
                          <div className="card w-100 border position-relative overflow-hidden mb-0">
                            <div className="px-4 py-3 border-bottom">
                              <div className="d-sm-flex align-items-center justify-space-between">
                                <h4 className="card-title mb-0">
                                  Inventory List
                                </h4>
                              </div>
                            </div>
                            <Inventory
                              productDetailId={productId}
                              renderInTab={true}
                              filters={["branch", "stock-status", "status", "manufacturing-date", "expiry-date"]}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
