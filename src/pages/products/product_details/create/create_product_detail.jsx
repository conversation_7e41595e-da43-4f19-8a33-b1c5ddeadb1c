import TopBar from "../../../../components/layout/topBar";
import Breadcrumb from "../../../../components/breadcrumb";
import { handleApiErrors } from "../../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../../../components/formikField";
import {
  useCreateProductDetailMutation,
  useGetVariationsProductsMutation,
  useListAllProductQuery,
} from "../../../../feature/api/productDataApiSlice";
import { Link, useNavigate } from "react-router-dom";
import { useListAllCategoriesQuery } from "../../../../feature/api/categoriesDataApiSlice";
import { useEffect, useMemo, useRef, useState } from "react";
import WebLoader from "../../../../components/webLoader";
import { useSelector } from "react-redux";
import { useListAllAttributesQuery } from "../../../../feature/api/attributesDataApiSlice";
import { useGetAllAttributesValuesQuery } from "../../../../feature/api/attributesValuesDataApiSlice";
import { useGetShopBranchsQuery } from "../../../../feature/api/branchDataApiSlice";

export default function CreateProductDetail() {
  const navigate = useNavigate();
  const activePage = "Products Master";
  const linkHref = "/dashboard";
  const [selectedBranchId, setSelectedBranchId] = useState("");
  const [selectedProductId, setSelectedProductId] = useState("");
  const [detailsDescription, setDetailsDescription] = useState("");
  const [detailsDescriptionAr, setDetailsDescriptionAr] = useState("");
  const [selectedVariations, setSelectedVariations] = useState([]);

  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector(
    (state) => state.commonState.generalStatus
  );
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list General Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Category List ******************* */
  const categoryListResp = useListAllCategoriesQuery();
  const categoryList = categoryListResp.data?.data || [];
  const categoriesList = categoryList.map((values) => ({
    value: values.id,
    label: values.title,
  }));
  /* **************** End Category List ******************* */

  const [selectedCategoryId, setSelectedCategoryId] = useState(null);

  const handleCategoryChange = (e) => {
    setSelectedCategoryId(parseInt(e.target.value) || null);
  };

  const { data: productsData, isLoading: isProductsLoading } = useListAllProductQuery({
    category_id: parseInt(selectedCategoryId),
    branch_id: parseInt(selectedBranchId),
  });

  const productsList = useMemo(() => {
    if (!productsData?.data?.length) return [];
    return productsData.data.map((product) => ({
      value: product.id,
      label: product.title,
      branch_id: product.branch_id,
      description: product.description,
      description_ar: product.description_ar,
    }));
  }, [productsData?.data]);
  /* **************** End Fetch Products ******************* */

  const handleProductChange = (e) => {    
    setSelectedProductId(e.target.value);
    const selectedProduct = productsList.find(
        (product) => parseInt(product.value) === parseInt(e.target.value)
      );
      if (selectedProduct) {
        setSelectedBranchId(selectedProduct.branch_id);
        setDetailsDescription(selectedProduct.description);
        setDetailsDescriptionAr(selectedProduct.description_ar);
      }
    };

  /* **************** Start Fetch Product Variations and Attributes ******************* */
  const [getVariationsProducts] = useGetVariationsProductsMutation();
  const [productAttributes, setProductAttributes] = useState([]);

  useEffect(() => {
    if (selectedProductId && selectedProductId !== "") {
      const fetchProductVariations = async () => {
        try {
          const response = await getVariationsProducts({
            product_id: parseInt(selectedProductId),
          }).unwrap();
          if (
            response?.data &&
            Array.isArray(response.data) &&
            response.data.length > 0
          ) {
            setProductAttributes(response.data);
            setSelectedVariations(response.data.map(attr => attr.attribute_id));
          } else {
            setProductAttributes([]);
            setSelectedVariations([]);
          }
        } catch (error) {
          console.error("Error fetching product variations:", error);
          setProductAttributes([]);
          setSelectedVariations([]);
        }
      };
      fetchProductVariations();
    } else {
      setProductAttributes([]);
      setSelectedVariations([]);
    }
  }, [selectedProductId, getVariationsProducts]);

  const { data: allAttributesData } = useListAllAttributesQuery();
  const attributesMap = useMemo(() => {
    if (!allAttributesData?.data) return {};
    return allAttributesData.data.reduce((acc, attr) => {
      acc[attr.id] = attr.title;
      return acc;
    }, {});
  }, [allAttributesData?.data]);

    const getProductDetailValidationSchema = (productAttributes) => {
    const baseValidation = {
      bar_code: yup.string().required().label("Barcode"),
      description: yup.string().label("Description"),
      description_ar: yup.string().label("Description Arabic"),
      is_publish: yup.string().required().label("Status"),
    };

    // Add validation for each attribute
    productAttributes.forEach((attr) => {
      const fieldName = `attribute_${attr.attribute_id}`;
      baseValidation[fieldName] = yup
        .string()
        .required()
        .label(
          attributesMap[attr.attribute_id] || `Attribute ${attr.attribute_id}`
        );
    });

    return yup.object().shape(baseValidation);
  };
  /* **************** End Fetch Product Variations and Attributes ******************* */

  const AttributeValueSelect = ({ attributeId, attributeTitle }) => {
    const {
      data: attributeValuesData,
      isLoading,
      error,
    } = useGetAllAttributesValuesQuery(
      { attribute_id: attributeId },
      { skip: !attributeId }
    );

    const attributeValueOptions = useMemo(() => {
      if (!attributeValuesData?.data?.length) return [];
      return attributeValuesData.data.map((value) => ({
        value: value.id,
        label: value.title,
      }));
    }, [attributeValuesData?.data]);

    return (
      attributeValueOptions.length > 0 &&
      !isLoading &&
      !error && (
        <div className="col-lg-6">
          <div className="mb-3">
            <label htmlFor={`attribute_${attributeId}`} className="form-label">
              {attributeTitle}
              <span className="un-validation">(*)</span>
              {isLoading && <small className="text-muted"> (Loading...)</small>}
            </label>
            <FormikField
              name={`attribute_${attributeId}`}
              id={`attribute_${attributeId}`}
              className="form-select"
              type="select"
              options={attributeValueOptions}
            />
            {error && (
              <small className="text-danger">
                Error loading attribute values
              </small>
            )}
          </div>
        </div>
      )
    );
  };

  const [
    handleCreateProductDetailsApi,
    { isLoading: isCreateProductDetailsLoading },
  ] = useCreateProductDetailMutation();

  const handleFinalSubmit = async (body) => {
    try {
      const attributes = [];
      Object.keys(body).forEach((key) => {
        if (key.startsWith("attribute_") && body[key] && body[key] !== "") {
          attributes.push(parseInt(body[key]));
        }
      });

      const productDetail = {
        branch_id: parseInt(selectedBranchId),
        product_id: parseInt(body.product_id),
        attributes: attributes.length > 0 ? JSON.stringify(attributes) : null,
        bar_code: body.bar_code,
        description: body.description || "",
        description_ar: body.description_ar || "",
        is_publish: parseInt(body.is_publish),
      };

      const detailResp = await handleCreateProductDetailsApi(
        productDetail
      ).unwrap();
      handleApiSuccess(detailResp);
      navigate("/productDetails");
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Form Values Storage ******************* */
  const currentFormValuesRef = useRef({});

  const getProductDetailInitialValues = (
    productAttributes,
    currentValues = {}
  ) => {
    const baseValues = {
      product_id: currentValues.product_id || selectedProductId || "",
      bar_code: currentValues.bar_code || "",
      description: currentValues.description || detailsDescription || "",
      description_ar: currentValues.description_ar || detailsDescriptionAr || "",
      is_publish: currentValues.is_publish || "",
    };

    // Add dynamic attribute fields
    selectedVariations.forEach((attrId) => {
      const fieldName = `attribute_${attrId}`;
      baseValues[fieldName] = currentValues[fieldName] || "";
    });

    return baseValues;
  };

  /* **************** Web Loader ******************* */
  if (isCreateProductDetailsLoading || isProductsLoading) return <WebLoader />;
  /* **************** End Web Loader ******************* */

  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />

            <div className="card">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          to={"/productDetails"}
                          className="btn btn-primary"
                        >
                          Product Details Listing
                        </Link>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body wizard-content">
                <div className="row">
                  <div className="col-lg-12 d-flex align-items-stretch">
                    <div className="card w-100 border position-relative overflow-hidden mb-0">
                      <div className="card-body p-4">
                        <h4 className="card-title">Create Product Details</h4>
                        <p className="card-subtitle mb-4">
                          To create Product Detail, add details and save from here
                        </p>
                        <Formik
                          initialValues={getProductDetailInitialValues(
                            productAttributes,
                            currentFormValuesRef.current
                          )}
                          enableReinitialize={true}
                          validationSchema={getProductDetailValidationSchema(productAttributes, attributesMap)}
                          onSubmit={handleFinalSubmit}
                        >
                          {(formikProps) => {
                            currentFormValuesRef.current = formikProps.values;
                            return (
                              <Form
                                name="product-details-create"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="row">
                                  <hr />
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="branch_id"
                                        className="form-label"
                                      >
                                        Branch
                                      </label>
                                      <select
                                        id="branch_id"
                                        className="form-select"
                                        value={selectedBranchId}
                                        onChange={(e) =>
                                          setSelectedBranchId(e.target.value)
                                        }
                                      >
                                        <option value="">Select Branch</option>
                                        {branchesList.map((branch) => (
                                          <option
                                            key={branch.value}
                                            value={branch.value}
                                          >
                                            {branch.label}
                                          </option>
                                        ))}
                                      </select>
                                    </div>
                                  </div>
                                   <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="category_id"
                                        className="form-label"
                                      >
                                        Category
                                      </label>
                                      <select
                                        id="category_id"
                                        className="form-select"
                                        onChange={handleCategoryChange}
                                      >
                                        <option value="">Select Category</option>
                                        {categoriesList.map((category) => (
                                          <option
                                            key={category.value}
                                            value={category.value}
                                          >
                                            {category.label}
                                          </option>
                                        ))}
                                      </select>
                                    </div>
                                  </div>
                                  <hr></hr>                                  
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="product_id"
                                        className="form-label"
                                      >
                                        Product{" "}
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="product_id"
                                        id="product_id"
                                        className="form-select"
                                        type="select"
                                        options={productsList}
                                        onChange={(e) => {
                                          handleProductChange(e);
                                        }}
                                      />
                                    </div>
                                  </div>
                                  {/* Start Dynamic Attribute Value Select Fields */}
                                  {productAttributes.length > 0 && (
                                    <>
                                      {productAttributes.map((attr) => (
                                        <AttributeValueSelect
                                          key={attr.attribute_id}
                                          attributeId={attr.attribute_id}
                                          attributeTitle={
                                            attributesMap[attr.attribute_id] ||
                                            `Attribute ${attr.attribute_id}`
                                          }
                                        />
                                      ))}
                                    </>
                                  )}
                                  {/* End Dynamic Attribute Value Select Fields */}
                                  <div className="row">
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="bar_code"
                                        className="form-label"
                                      >
                                        Barcode{" "}
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="bar_code"
                                        id="bar_code"
                                        placeholder="Enter barcode *"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6"></div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="description"
                                        className="form-label"
                                      >
                                        Description
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="description"
                                        id="description"
                                        placeholder="Enter description"
                                        className="form-control"
                                        value={detailsDescription}
                                        onChange={(e) => {
                                          setDetailsDescription(e.target.value);
                                          formikProps.handleChange(e);
                                        }}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="description_ar"
                                        className="form-label"
                                      >
                                        Description Arabic
                                      </label>
                                      <FormikField
                                        type="textarea"
                                        name="description_ar"
                                        id="description_ar"
                                        placeholder="Enter description arabic"
                                        className="form-control"
                                        value={detailsDescriptionAr}
                                        onChange={(e) => {
                                          setDetailsDescriptionAr(e.target.value);
                                          formikProps.handleChange(e);
                                        }}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-lg-6">
                                    <div className="mb-3">
                                      <label
                                        htmlFor="is_publish"
                                        className="form-label"
                                      >
                                        Status{" "}
                                        <span className="un-validation">(*)</span>
                                      </label>
                                      <FormikField
                                        name="is_publish"
                                        id="is_publish"
                                        className="form-select"
                                        type="select"
                                        options={generalStatusList}
                                      />
                                    </div>
                                  </div>
                                  </div>
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Create
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                            );
                          }}
                        </Formik>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}