import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useListAllBrandsQuery } from "../../../feature/api/brandsDataApiSlice";
import FormikField from "../../../components/formikField";
import {
  useCreateProductDetailMutation,
  useCreateProductMutation,
  useCreateUpdateVariationsProductsMutation,
  useEditProductMutation,
  useGetVariationsProductsMutation,
} from "../../../feature/api/productDataApiSlice";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useListAllCategoriesQuery } from "../../../feature/api/categoriesDataApiSlice";
import { useGetAllSubCategoriesQuery } from "../../../feature/api/subCategoriesDataApiSlice";
import { useGetAllChildCategoriesQuery } from "../../../feature/api/childCategoriesDataApiSlice";
import { useEffect, useMemo, useRef, useState } from "react";
import WebLoader from "../../../components/webLoader";
import { useSelector } from "react-redux";
import { useListAllAttributesQuery } from "../../../feature/api/attributesDataApiSlice";
import { useGetAllAttributesValuesQuery } from "../../../feature/api/attributesValuesDataApiSlice";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";

const validation = yup.object().shape({
  branch_id: yup.string().required().label("Branch"),
  image: yup.mixed().required().label("Product Image"),
  title: yup.string().required().label("Product Title in English"),
  title_ar: yup.string().required().label("Product Title in Arabic"),
  brand_id: yup.string().required().label("Brand"),
  category_id: yup.string().required().label("Category"),
  sub_category_id: yup.string().label("Sub Category"),
  child_category_id: yup.string().label("Child Category"),
  description: yup.string().label("Description in English"),
  description_ar: yup.string().label("Description in Arabic"),
  is_publish: yup.string().required().label("Status"),
  note: yup.string().label("Note"),
});

export default function CreateProduct() {
  const { state } = useLocation();
  const copyProductData = state?.d;
  const navigate = useNavigate();
  const activePage = "Products Master";
  const linkHref = "/dashboard";
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedBranchId, setSelectedBranchId] = useState("");
  const [selectedProductId, setSelectedProductId] = useState("");
  const [detailsDescription, setDetailsDescription] = useState("");
  const [detailsDescriptionAr, setDetailsDescriptionAr] = useState("");
  const [selectedVariations, setSelectedVariations] = useState([]);
  const formikRef = useRef(null);
  const detailFormikRef = useRef(null);
  const initialValues = {
    branch_id: copyProductData?.branch_id || "",
    image: copyProductData?.image || null,
    title: copyProductData?.title || "",
    title_ar: copyProductData?.title_ar || "",
    brand_id: copyProductData?.brand_id || "",
    category_id: copyProductData?.category_id || "",
    sub_category_id: copyProductData?.sub_category_id || "",
    child_category_id: copyProductData?.child_category_id || "",
    description: copyProductData?.description || "",
    description_ar: copyProductData?.description_ar || "",
    is_publish: copyProductData?.is_publish || "",
    note: copyProductData?.note || "",
    attribute_id: [],
  };

  const [formValues, setFormValues] = useState(initialValues);

  const goToNext = async () => {
    if (currentStep < 2) {
      // Validate and submit form
      const errors = await formikRef.current.validateForm();
      if (Object.keys(errors).length === 0) {
        formikRef.current.submitForm(); // Submit the basic information form
      } else {
        formikRef.current.submitForm(); // Trigger validation errors
      }
    }
  };

  const goToPrevious = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  /* **************** Start list General Status ******************* */
  const generalStatuData = useSelector(
    (state) => state.commonState.generalStatus
  );
  const generalStatuDataList = generalStatuData?.data || [];
  const generalStatusList = generalStatuDataList.map((values) => ({
    value: values.id,
    label: values.status,
  }));
  /* **************** End list General Status ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start List Brand List ******************* */
  const brandResp = useListAllBrandsQuery();
  const brand = brandResp.data?.data || [];
  const brandAry = brand.map((brand) => ({
    value: brand.id,
    label: brand.title,
  }));
  /* **************** End List Brand List ******************* */

  /* **************** Start List Attributes List ******************* */
  const attributeResp = useListAllAttributesQuery();
  const attribute = attributeResp.data?.data || [];
  const attributesAry = attribute.map((attribute) => ({
    value: attribute.id,
    label: attribute.title,
  }));
  /* **************** End List Attributes List ******************* */

  /* **************** Start Category List ******************* */
  const categoryListResp = useListAllCategoriesQuery();
  const categoryList = categoryListResp.data?.data || [];
  const categoriesList = categoryList.map((values) => ({
    value: values.id,
    label: values.title,
  }));
  /* **************** End Category List ******************* */

  /* **************** Start Fetch Subcategories ******************* */
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);
  const { data: subCategoriesData } = useGetAllSubCategoriesQuery(
    { category_id: selectedCategoryId },
    { skip: !selectedCategoryId }
  );
  const subCategoriesList = useMemo(() => {
    if (!subCategoriesData?.data?.length) return [];
    return subCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [subCategoriesData?.data]);
  /* **************** End Fetch Subcategories ******************* */

  const handleCategoryChange = (e) => {
    setSelectedCategoryId(parseInt(e.target.value));
  };

  /* **************** Start Fetch Child Categories ******************* */
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(null);
  const { data: childCategoriesData } = useGetAllChildCategoriesQuery(
    { sub_category_id: selectedSubCategoryId },
    { skip: !selectedSubCategoryId }
  );
  const childCategoriesList = useMemo(() => {
    if (!childCategoriesData?.data?.length) return [];
    return childCategoriesData.data.map((values) => ({
      value: values.id,
      label: values.title,
    }));
  }, [childCategoriesData?.data]);
  /* **************** End Fetch Child Categories ******************* */

  const handleSubCategoryChange = (e) => {
    setSelectedSubCategoryId(parseInt(e.target.value));
  };

  /* **************** Start Fetch Product Variations and Attributes ******************* */
  const [getVariationsProducts] = useGetVariationsProductsMutation();
  const [productAttributes, setProductAttributes] = useState([]);

  useEffect(() => {
    if (selectedProductId && selectedProductId !== "") {
      const fetchProductVariations = async () => {
        try {
          const response = await getVariationsProducts({
            product_id: parseInt(selectedProductId),
          }).unwrap();
          if (
            response?.data &&
            Array.isArray(response.data) &&
            response.data.length > 0
          ) {
            setProductAttributes(response.data);
          } else {
            setProductAttributes([]);
          }
        } catch (error) {
          console.error("Error fetching product variations:", error);
          setProductAttributes([]);
        }
      };
      fetchProductVariations();
    } else {
      setProductAttributes(
        selectedVariations.map((id) => ({ attribute_id: parseInt(id) }))
      );
    }
  }, [selectedProductId, selectedVariations, getVariationsProducts]);

  const { data: allAttributesData } = useListAllAttributesQuery();
  const attributesMap = useMemo(() => {
    if (!allAttributesData?.data) return {};
    return allAttributesData.data.reduce((acc, attr) => {
      acc[attr.id] = attr.title;
      return acc;
    }, {});
  }, [allAttributesData?.data]);
  /* **************** End Fetch Product Variations and Attributes ******************* */

  const getProductDetailValidationSchema = (productAttributes) => {
    const baseValidation = {
      bar_code: yup.string().required().label("Barcode"),
      description: yup.string().label("Description"),
      description_ar: yup.string().label("Description Arabic"),
      is_publish: yup.string().required().label("Status"),
    };

    // Add validation for each attribute
    productAttributes.forEach((attr) => {
      const fieldName = `attribute_${attr.attribute_id}`;
      baseValidation[fieldName] = yup
        .string()
        .required()
        .label(
          attributesMap[attr.attribute_id] || `Attribute ${attr.attribute_id}`
        );
    });

    return yup.object().shape(baseValidation);
  };

  const AttributeValueSelect = ({ attributeId, attributeTitle }) => {
    const {
      data: attributeValuesData,
      isLoading,
      error,
    } = useGetAllAttributesValuesQuery(
      { attribute_id: attributeId },
      { skip: !attributeId }
    );

    const attributeValueOptions = useMemo(() => {
      if (!attributeValuesData?.data?.length) return [];
      return attributeValuesData.data.map((value) => ({
        value: value.id,
        label: value.title,
      }));
    }, [attributeValuesData?.data]);

    return (
      attributeValueOptions.length > 0 &&
      !isLoading &&
      !error && (
        <div className="col-lg-6">
          <div className="mb-3">
            <label htmlFor={`attribute_${attributeId}`} className="form-label">
              {attributeTitle}
              <span className="un-validation">(*)</span>
              {isLoading && <small className="text-muted"> (Loading...)</small>}
            </label>
            <FormikField
              name={`attribute_${attributeId}`}
              id={`attribute_${attributeId}`}
              className="form-select"
              type="select"
              options={attributeValueOptions}
            />
            {error && (
              <small className="text-danger">
                Error loading attribute values
              </small>
            )}
          </div>
        </div>
      )
    );
  };

  /* **************** API Handlers ******************* */
  const [handleCreateProductApi, { isLoading: isLoadingProduct }] =
    useCreateProductMutation();
  const [handleUpdateProductApi, { isLoading: isLoadingEditProduct }] =
    useEditProductMutation();
  const [
    handleAssignVariationDataApi,
    { isLoading: isAssignVariationLoading },
  ] = useCreateUpdateVariationsProductsMutation();
  const [
    handleCreateProductDetailsApi,
    { isLoading: isCreateProductDetailsLoading },
  ] = useCreateProductDetailMutation();

  const handleCreateUpdateProduct = async (body) => {
    try {
      const formData = new FormData();
      for (const [key, value] of Object.entries(body)) {
        if (key === "image" && value) {
          formData.append(key, value);
        } else if (
          key === "is_publish" ||
          key === "category_id" ||
          (key === "sub_category_id" && value != "") ||
          (key === "child_category_id" && value != "") ||
          key === "brand_id" ||
          key === "branch_id"
        ) {
          formData.append(key, parseInt(value));
        } else {
          formData.append(key, value);
        }
      }

      let productResp;
      if (selectedProductId) {
        formData.append("id", parseInt(selectedProductId));
        productResp = await handleUpdateProductApi(formData).unwrap();
      } else {
        productResp = await handleCreateProductApi(formData).unwrap();
        setSelectedProductId(productResp.data);
      }

      // 1. Assign Variations (if any selected or in edit mode)

      if (selectedVariations.length > 0 || selectedProductId) {
        const variationBody = {
          product_id: parseInt(selectedProductId || productResp.data),
          attribute_id: selectedVariations.map((id) => parseInt(id)),
        };
        await handleAssignVariationDataApi(variationBody).unwrap();
      }

      setSelectedBranchId(body.branch_id);
      setDetailsDescription(body.description);
      setDetailsDescriptionAr(body.description_ar);
      setSelectedVariations(body.attribute_id || []);
      setCurrentStep(2);
    } catch (error) {
      handleApiErrors(error);
    }
    setFormValues(body);
  };

  const handleFinalSubmit = async (body) => {
    try {
      let productId = selectedProductId;

      const attributes = [];
      Object.keys(body).forEach((key) => {
        if (key.startsWith("attribute_") && body[key] && body[key] !== "") {
          attributes.push(parseInt(body[key]));
        }
      });

      const productDetail = {
        branch_id: parseInt(selectedBranchId),
        product_id: parseInt(productId),
        attributes: attributes.length > 0 ? JSON.stringify(attributes) : null,
        bar_code: body.bar_code,
        description: body.description || "",
        description_ar: body.description_ar || "",
        is_publish: parseInt(body.is_publish),
      };

      const detailResp = await handleCreateProductDetailsApi(
        productDetail
      ).unwrap();
      handleApiSuccess(detailResp);
      navigate(`/viewProduct/${selectedProductId}/product-variations`);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Form Values Storage ******************* */
  const currentFormValuesRef = useRef({});

  const getProductDetailInitialValues = (
    productAttributes,
    currentValues = {}
  ) => {
    const baseValues = {
      product_id: currentValues.product_id || selectedProductId || "",
      bar_code: currentValues.bar_code || "",
      description: currentValues.description || detailsDescription || "",
      description_ar:
        currentValues.description_ar || detailsDescriptionAr || "",
      is_publish: currentValues.is_publish || "",
    };

    // Add dynamic attribute fields
    selectedVariations.forEach((attrId) => {
      const fieldName = `attribute_${attrId}`;
      baseValues[fieldName] = currentValues[fieldName] || "";
    });

    return baseValues;
  };

  /* **************** Web Loader ******************* */
  if (
    isLoadingProduct ||
    isAssignVariationLoading ||
    isCreateProductDetailsLoading ||
    isLoadingEditProduct
  )
    return <WebLoader />;
  /* **************** End Web Loader ******************* */

  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />

            <div className="card">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          to={"/products"}
                          className="btn btn-primary"
                        >
                          Product Listing
                        </Link>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body wizard-content">
                <h4 className="card-title mb-0">Create Product</h4>
                <form className="tab-wizard wizard-circle wizard" noValidate>
                  {/* Steps Header */}
                  <div className="steps clearfix">
                    <ul role="tablist">
                      <li
                        role="tab"
                        className={`stepRoundUn stepRoundUn_1 ${
                          currentStep === 1 ? "current" : ""
                        }`}
                        data-step-count="1"
                      >
                        <a
                          style={{ cursor: "pointer" }}
                          onClick={(e) => {
                            e.preventDefault();
                            selectedProductId && goToPrevious();
                          }}
                        >
                          <span className="step">1</span> Product Information
                        </a>
                      </li>
                      <li
                        role="tab"
                        className={`stepRoundUn stepRoundUn_2 ${
                          currentStep === 2 ? "current" : "disabled"
                        }`}
                        data-step-count="2"
                      >
                        <a
                          style={{ cursor: "pointer" }}
                          onClick={(e) => {
                            e.preventDefault();
                            selectedProductId && goToNext();
                          }}
                        >
                          <span className="step">2</span> Product Details
                        </a>
                      </li>
                    </ul>
                  </div>

                  {/* Content Sections */}
                  <div className="content clearfix">
                    {/* Step 1 */}
                    <section
                      id="steps-uid-0-p-0"
                      role="tabpanel"
                      aria-labelledby="steps-uid-0-h-0"
                      className={`body ${currentStep === 1 ? "current" : ""}`}
                      style={{ display: currentStep === 1 ? "block" : "none" }}
                    >
                      <div className="row">
                        <div className="col-lg-12 d-flex align-items-stretch">
                          <div className="card w-100 border position-relative overflow-hidden mb-0">
                            <div className="card-body p-4">
                              <h4 className="card-title">Product Basics</h4>
                              <p className="card-subtitle mb-4">
                                To create Product Basics, add details and save
                                from here
                              </p>
                              <Formik
                                innerRef={formikRef}
                                initialValues={formValues}
                                validationSchema={validation}
                                onSubmit={handleCreateUpdateProduct}
                                enableReinitialize={true}
                              >
                                <Form
                                  name="product-create"
                                  className="needs-validation"
                                  autoComplete="on"
                                  encType="multipart/form-data"
                                >
                                  <div className="row">
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="branch_id"
                                          className="form-label"
                                        >
                                          Branch{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          name="branch_id"
                                          id="branch_id"
                                          className="form-select"
                                          type="select"
                                          options={branchesList}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6"></div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="category_id"
                                          className="form-label"
                                        >
                                          Category{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          name="category_id"
                                          id="category_id"
                                          className="form-select"
                                          type="select"
                                          options={categoriesList}
                                          onChange={handleCategoryChange}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="sub_category_id"
                                          className="form-label"
                                        >
                                          Sub Category
                                        </label>
                                        <FormikField
                                          name="sub_category_id"
                                          id="sub_category_id"
                                          className="form-select"
                                          type="select"
                                          options={subCategoriesList}
                                          onChange={handleSubCategoryChange}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="child_category_id"
                                          className="form-label"
                                        >
                                          Child Category
                                        </label>
                                        <FormikField
                                          name="child_category_id"
                                          id="child_category_id"
                                          className="form-select"
                                          type="select"
                                          options={childCategoriesList}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="brand_id"
                                          className="form-label"
                                        >
                                          Brands{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          name="brand_id"
                                          id="brand_id"
                                          className="form-select"
                                          type="select"
                                          options={brandAry}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="title"
                                          className="form-label"
                                        >
                                          Product Title in English{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="title"
                                          id="title"
                                          placeholder="Product Title in English *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="title_ar"
                                          className="form-label"
                                        >
                                          Product Title in Arabic{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="text"
                                          name="title_ar"
                                          id="title_ar"
                                          placeholder="Product Title in Arabic *"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="description"
                                          className="form-label"
                                        >
                                          Product Description in English
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="description"
                                          id="description"
                                          placeholder="Product Description in English"
                                          autoComplete="off"
                                          className="form-control"
                                          onInput={(e) => {
                                            setDetailsDescription(
                                              e.target.value
                                            );
                                          }}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="description_ar"
                                          className="form-label"
                                        >
                                          Product Description in Arabic
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="description_ar"
                                          id="description_ar"
                                          placeholder="Product Description in Arabic"
                                          autoComplete="off"
                                          className="form-control"
                                          onInput={(e) => {
                                            setDetailsDescriptionAr(
                                              e.target.value
                                            );
                                          }}
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="note"
                                          className="form-label"
                                        >
                                          Note
                                        </label>
                                        <FormikField
                                          type="textarea"
                                          name="note"
                                          id="note"
                                          placeholder="Note"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="image"
                                          className="form-label"
                                        >
                                          Product Image{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          type="file"
                                          name="image"
                                          id="image"
                                          autoComplete="off"
                                          className="form-control"
                                        />
                                      </div>
                                    </div>
                                    <div className="col-lg-6">
                                      <div className="mb-3">
                                        <label
                                          htmlFor="is_publish"
                                          className="form-label"
                                        >
                                          Product Status{" "}
                                          <span className="un-validation">
                                            (*)
                                          </span>
                                        </label>
                                        <FormikField
                                          name="is_publish"
                                          id="is_publish"
                                          className="form-select"
                                          type="select"
                                          options={generalStatusList}
                                        />
                                      </div>
                                    </div>
                                    {attributesAry.length > 0 && (
                                      <>
                                        <hr />
                                        <div className="col-lg-12">
                                          <div
                                            className="alert customize-alert alert-dismissible text-primary alert-light-primary bg-primary-subtle fade show remove-close-icon"
                                            role="alert"
                                          >
                                            <span className="side-line bg-primary"></span>
                                            <div className="d-flex align-items-center ">
                                              <i className="ti ti-info-circle fs-5 text-primary me-2 flex-shrink-0"></i>
                                              <span className="">
                                                Note: If your product has
                                                variations such as color, size,
                                                material etc, please select the
                                                appropriate options below. This
                                                will create distinct versions
                                                (SKUs) for each combination,
                                                allowing for accurate inventory
                                                management, pricing, and
                                                tracking. If your product
                                                variations are not listed here,
                                                please go to the{" "}
                                                <Link
                                                  className="alert-link"
                                                  to="/attributes"
                                                >
                                                  {" "}
                                                  Attributes
                                                </Link>{" "}
                                                and{" "}
                                                <Link
                                                  className="alert-link"
                                                  to="/attributesValues"
                                                >
                                                  {" "}
                                                  Attribute Values
                                                </Link>{" "}
                                                modules to add them first.
                                              </span>
                                            </div>
                                          </div>
                                          <div className="mb-3">
                                            <label
                                              htmlFor="attribute_id"
                                              className="form-label"
                                            >
                                              Assign Product Variations{" "}
                                              <span className="ms-1">
                                                (Optional)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="attribute_id"
                                              id="attribute_id"
                                              type="checkbox-group"
                                              options={attributesAry}
                                              align="horizontal"
                                              onChange={(e, setFieldValue) => {
                                                const values = e.target.checked
                                                  ? [
                                                      ...selectedVariations,
                                                      e.target.value,
                                                    ]
                                                  : selectedVariations.filter(
                                                      (id) =>
                                                        id !== e.target.value
                                                    );
                                                setSelectedVariations(values);
                                                setFieldValue(
                                                  "attribute_id",
                                                  values
                                                );
                                              }}
                                            />
                                          </div>
                                        </div>
                                      </>
                                    )}
                                  </div>
                                </Form>
                              </Formik>
                            </div>
                          </div>
                        </div>
                      </div>
                    </section>

                    {/* Step 2 */}
                    <section
                      id="steps-uid-0-p-1"
                      role="tabpanel"
                      aria-labelledby="steps-uid-0-h-1"
                      className={`body ${currentStep === 2 ? "current" : ""}`}
                      style={{ display: currentStep === 2 ? "block" : "none" }}
                    >
                      <div className="row">
                        <div className="col-lg-12 d-flex align-items-stretch">
                          <div className="card w-100 border position-relative overflow-hidden mb-0">
                            <div className="card-body p-4">
                              <h4 className="card-title">Product Details</h4>
                              <p className="card-subtitle mb-4">
                                To create Product, add details and save from
                                here
                              </p>
                              <Formik
                                innerRef={detailFormikRef}
                                initialValues={getProductDetailInitialValues(
                                  productAttributes,
                                  currentFormValuesRef.current
                                )}
                                enableReinitialize={true}
                                validationSchema={getProductDetailValidationSchema(
                                  productAttributes
                                )}
                                onSubmit={handleFinalSubmit}
                              >
                                {(formikProps) => {
                                  currentFormValuesRef.current =
                                    formikProps.values;
                                  return (
                                    <Form
                                      name="product-details-create"
                                      className="needs-validation"
                                      autoComplete="off"
                                    >
                                      <div className="row">
                                        <hr />
                                        <h4 className="card-title">
                                          Product Details
                                        </h4>
                                        {/* Start Dynamic Attribute Value Select Fields */}
                                        {productAttributes.length > 0 && (
                                          <>
                                            {productAttributes.map((attr) => (
                                              <AttributeValueSelect
                                                key={attr.attribute_id}
                                                attributeId={attr.attribute_id}
                                                attributeTitle={
                                                  attributesMap[
                                                    attr.attribute_id
                                                  ] ||
                                                  `Attribute ${attr.attribute_id}`
                                                }
                                              />
                                            ))}
                                          </>
                                        )}
                                        {/* End Dynamic Attribute Value Select Fields */}
                                        {/* <hr></hr> */}
                                        <div className="row">
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="bar_code"
                                                className="form-label"
                                              >
                                                Barcode{" "}
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                type="text"
                                                name="bar_code"
                                                id="bar_code"
                                                placeholder="Enter barcode *"
                                                className="form-control"
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6"></div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="description"
                                                className="form-label"
                                              >
                                                Description
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="description"
                                                id="description"
                                                placeholder="Enter description"
                                                className="form-control"
                                                value={detailsDescription}
                                                onChange={(e) => {
                                                  setDetailsDescription(
                                                    e.target.value
                                                  );
                                                }}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="description_ar"
                                                className="form-label"
                                              >
                                                Description Arabic
                                              </label>
                                              <FormikField
                                                type="textarea"
                                                name="description_ar"
                                                id="description_ar"
                                                placeholder="Enter description arabic"
                                                className="form-control"
                                                value={detailsDescriptionAr}
                                                onChange={(e) => {
                                                  setDetailsDescriptionAr(
                                                    e.target.value
                                                  );
                                                }}
                                              />
                                            </div>
                                          </div>
                                          <div className="col-lg-6">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="is_publish"
                                                className="form-label"
                                              >
                                                Status{" "}
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="is_publish"
                                                id="is_publish"
                                                className="form-select"
                                                type="select"
                                                options={generalStatusList}
                                              />
                                            </div>
                                          </div>
                                        </div>
                                        <div className="col-12">
                                          <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                            <button
                                              className="btn btn-primary"
                                              type="submit"
                                              onClick={(e) => {
                                                e.preventDefault();
                                                detailFormikRef.current?.submitForm();
                                              }}
                                            >
                                              Submit All
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    </Form>
                                  );
                                }}
                              </Formik>
                            </div>
                          </div>
                        </div>
                      </div>
                    </section>
                  </div>

                  {/* Navigation Buttons */}
                  <div className="actions clearfix">
                    <ul role="menu" aria-label="Pagination">
                      <li className={currentStep === 1 ? "disabled" : ""}>
                        <a
                          href="#previous"
                          role="menuitem"
                          onClick={(e) => {
                            e.preventDefault();
                            goToPrevious();
                          }}
                        >
                          Previous
                        </a>
                      </li>
                      {currentStep < 2 && (
                        <li>
                          <a
                            href="#next"
                            role="menuitem"
                            onClick={(e) => {
                              e.preventDefault();
                              goToNext();
                            }}
                          >
                            Next
                          </a>
                        </li>
                      )}
                    </ul>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
