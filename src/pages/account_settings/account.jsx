import { useEffect, useState } from "react";
import {
  useChangePasswordMutation,
  useGetProfileQuery,
  useUpdateProfileMutation,
} from "../../feature/api/authApiSlice";
import { handleApiErrors } from "../../hooks/handleApiErrors";
import { Formik, Form } from "formik";
import * as yup from "yup";
import FormikField from "../../components/formikField";
import TopBar from "../../components/layout/topBar";
import Breadcrumb from "../../components/breadcrumb";
import { handleApiSuccess } from "../../hooks/handleApiSucess";
import { useSelector } from "react-redux";
import WebLoader from "../../components/webLoader";

const validationChangePassword = yup.object().shape({
  current_password: yup.string().required().label("Current Password"),
  password: yup.string().min(8).required().label("New Password"),
  confirm_password: yup
    .string()
    .oneOf(
      [yup.ref("password"), null],
      "Confirm password must be equal to new password"
    )
    .required()
    .label("Confirm Password"),
});

export default function Account() {
  const user_type = useSelector((state) => state.authState.user_type);
  const activePage = "Account Settings";
  const linkHref = "/dashboard";
  const { data, isLoading, isError, error } = useGetProfileQuery();

  // Initialize with empty values, to be updated when data is available
  const initialValues = {
    name: "",
    qid: "",
    phone_code: "",
    phone: "",
    email: "",
    address: "",
  };

  const [profileFormValues, setProfileFormValues] = useState(initialValues);

  // Update profileFormValues when data is available
  useEffect(() => {
    if (data?.data) {
      setProfileFormValues({
        name: data?.data.name || "",
        qid: data?.data.qid || "",
        phone_code: data?.data.phone_code || "",
        phone: data?.data.phone || "",
        email: data?.data.email || "",
        address: data?.data.address || "",
      });
    }
  }, [data?.data]);

  const validation = yup.object().shape({
    name: yup.string().required().label("Full Name"),
    qid: yup
      .string()
      .length(11, "QID must be exactly 11 digits")
      .required()
      .label("QID"),
    phone: yup
      .string()
      .matches(/^\d{8}$/, "Phone number must be exactly 8 digits")
      .required()
      .label("Phone Number"),
    email: yup.string().email().required().label("Email Address"),
    address: yup.string().required().label("Address"),
  });

  const initialValuesChangePassword = {
    current_password: "",
    password: "",
    confirm_password: "",
  };

  const [changePasswordFormValues, setChangePasswordFormValues] = useState(initialValuesChangePassword);

  const countries = useSelector((state) => state.commonState.countries);
  const countriesList = countries?.data
    ? countries.data.map((values) => ({
        value: values.phone_code,
        label: values.name + ' (' + values.phone_code + ')',
      }))
    : [];

  useEffect(() => {
    if (!isError) return;
    handleApiErrors(error);
  }, [isError, error]);

  const [handleUpdateProfileApi, { isLoading: isLoading1 }] =
    useUpdateProfileMutation();
  const handleSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        user_type: user_type,
      };
      const resp = await handleUpdateProfileApi(updatedBody).unwrap();
      handleApiSuccess(resp);
    } catch (error) {
      handleApiErrors(error);
    }
    setProfileFormValues(body);
  };

  const [handleChangePasswordApi, { isLoading: isLoading2 }] =
    useChangePasswordMutation();
  const handleChangePasswordSubmit = async (body) => {
    try {
      const updatedBody = {
        ...body,
        user_type: user_type,
      };
      const resp = await handleChangePasswordApi(updatedBody).unwrap();
      handleApiSuccess(resp);
    } catch (error) {
      handleApiErrors(error);
    }
    setChangePasswordFormValues(body);
  };

  if (isLoading || isLoading1 || isLoading2) return <WebLoader />;

  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid ">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-6 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden">
                          <div className="card-body p-4">
                            <h4 className="card-title">Change Profile</h4>
                            <p className="card-subtitle mb-4">
                              Change your profile picture from here
                            </p>
                            <div className="text-center">
                              <img
                                src="/src/assets/images/profile/user-1.jpg"
                                alt="matdash-img"
                                className="img-fluid rounded-circle"
                                width="120"
                                height="120"
                              />
                              <div className="d-flex align-items-center justify-content-center my-4 gap-6">
                                <form>
                                  <input
                                    type="file"
                                    className="btn btn-light"
                                  />
                                  <button
                                    className="btn btn-primary"
                                    type="submit"
                                  >
                                    Upload
                                  </button>
                                </form>
                              </div>
                              <p className="mb-0">
                                Allowed JPG, GIF or PNG. Max size of 800KB
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="col-lg-6 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden">
                          <div className="card-body p-4">
                            <h4 className="card-title">Change Password</h4>
                            <p className="card-subtitle mb-4">
                              To change your password please confirm here
                            </p>
                            <Formik
                              initialValues={changePasswordFormValues}
                              validationSchema={validationChangePassword}
                              onSubmit={handleChangePasswordSubmit}
                            >
                              <Form
                                name="change-password"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="mb-3">
                                  <label
                                    htmlFor="current_password"
                                    className="form-label"
                                  >
                                    Current Password
                                  </label>
                                  <FormikField
                                    type="password"
                                    name="current_password"
                                    id="current_password"
                                    placeholder="Current Password *"
                                    autoComplete="off"
                                    className="form-control"
                                  />
                                </div>
                                <div className="mb-3">
                                  <label
                                    htmlFor="password"
                                    className="form-label"
                                  >
                                    New Password
                                  </label>
                                  <FormikField
                                    type="password"
                                    name="password"
                                    id="password"
                                    placeholder="New Password *"
                                    autoComplete="off"
                                    className="form-control"
                                  />
                                </div>
                                <div>
                                  <label
                                    htmlFor="confirm_password"
                                    className="form-label"
                                  >
                                    Confirm Password
                                  </label>
                                  <FormikField
                                    type="password"
                                    name="confirm_password"
                                    id="confirm_password"
                                    placeholder="Confirm Password *"
                                    autoComplete="off"
                                    className="form-control"
                                  />
                                </div>
                                <div>
                                  <br />
                                  <button
                                    className="btn btn-primary"
                                    type="submit"
                                  >
                                    Update Password
                                  </button>
                                </div>
                              </Form>
                            </Formik>
                          </div>
                        </div>
                      </div>
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">Personal Details</h4>
                            <p className="card-subtitle mb-4">
                              To change your personal detail, edit and save
                              from here
                            </p>
                            <Formik
                              initialValues={profileFormValues}
                              enableReinitialize
                              validationSchema={validation}
                              onSubmit={handleSubmit}
                            >
                              <Form
                                name="profile-update"
                                className="needs-validation"
                                autoComplete="off"
                              >
                                <div className="row">
                                  <div className="col-lg-12">
                                    <div className="row">
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="name"
                                            className="form-label"
                                          >
                                            Full Name
                                          </label>
                                          <FormikField
                                            type="text"
                                            name="name"
                                            id="name"
                                            placeholder="Enter Full Name *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-6">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="qid"
                                            className="form-label"
                                          >
                                            QID
                                          </label>
                                          <FormikField
                                            type="number"
                                            name="qid"
                                            id="qid"
                                            placeholder="Enter your QID *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                    <div className="mb-3">
                                      <label className="form-label">
                                        Phone Number
                                      </label>
                                      <div className="row">
                                        <div className="col-4">
                                          <FormikField
                                            name="phone_code"
                                            id="phone_code"
                                            className="form-select"
                                            type="select"
                                            options={countriesList}
                                          />
                                        </div>
                                        <div className="col-8">
                                          <FormikField
                                            type="number"
                                            name="phone"
                                            id="phone"
                                            placeholder="Your Phone Number *"
                                            autoComplete="off"
                                            className="form-control"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                    <div className="mb-3">
                                      <label
                                        htmlFor="email"
                                        className="form-label"
                                      >
                                        Email Address
                                      </label>
                                      <FormikField
                                        type="email"
                                        name="email"
                                        id="email"
                                        placeholder="Your Email Address *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-12">
                                    <div>
                                      <label
                                        htmlFor="address"
                                        className="form-label"
                                      >
                                        Address
                                      </label>
                                      <FormikField
                                        type="text"
                                        name="address"
                                        id="address"
                                        placeholder="Your Address *"
                                        autoComplete="off"
                                        className="form-control"
                                      />
                                    </div>
                                  </div>
                                  <div className="col-12">
                                    <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                      <button
                                        className="btn btn-primary"
                                        type="submit"
                                      >
                                        Update Personal Details
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </Form>
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}