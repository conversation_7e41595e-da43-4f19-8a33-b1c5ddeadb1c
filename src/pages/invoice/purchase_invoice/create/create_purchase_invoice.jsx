import TopBar from "../../../../components/layout/topBar";
import Breadcrumb from "../../../../components/breadcrumb";
import { handleApiErrors } from "../../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../../hooks/handleApiSucess";
import { Formik, Form, FieldArray, useFormikContext } from "formik";
import * as yup from "yup";
import FormikField from "../../../../components/formikField";
import { useListAllSupplierQuery } from "../../../../feature/api/supplierDataApiSlice";
import { useNavigate } from "react-router-dom";
import WebLoader from "../../../../components/webLoader";
import { useGetShopBranchsQuery } from "../../../../feature/api/branchDataApiSlice";
import { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useCreatePurchaseInvoiceMutation } from "../../../../feature/api/purchaseInvoiceDataApiSlice";
import { handleToast } from "../../../../hooks/handleToast";
import { formatDate, formatTime } from "../../../../utils/dateHelpers";
import { useGetAllProductdetailListQuery } from "../../../../feature/api/productDataApiSlice";
import { useListAllSupplierInvoiceQuery } from "../../../../feature/api/supplierInvoiceDataApiSlice";
import useConfirm from "../../../../hooks/useConfirm";

const formattedCurrentDate = formatDate(new Date().toLocaleDateString());
const formattedCurrentTime = formatTime(new Date().toLocaleTimeString());

const validation = yup.object().shape({
  branch_id: yup.string().required().label("Branch"),
  date: yup.string().required().label("Date"),
  time: yup.string().required().label("Time"),
  supplier_id: yup.string().required().label("Supplier"),
  supplier_invoice_id: yup.string().label("Supplier Invoice"),
  currency: yup.string().label("Currency"),
  exchange_rate: yup.number().required().min(0.0).label("Exchange Rate"),
  shipping_charge: yup.number().min(0.0).label("Shipping Charge"),
  transaction_fee: yup.number().min(0.0).label("Transaction Fee"),
  other_expense: yup.number().min(0.0).label("Other Expense"),
  invoice_type: yup.string().required().label("Invoice Type"),
});

function TotalsCalculator() {
  const { values, setFieldValue } = useFormikContext();
  useEffect(() => {
    values.items.forEach((item, index) => {
      const unitPrice = parseFloat(item.unit_price) || 0;
      const quantity = parseFloat(item.quantity) || 0;
      const exchangeRate = parseFloat(values.exchange_rate) || 1.0;

      const totalPrice = parseFloat(unitPrice * quantity).toFixed(2);
      const totalPriceInShopCurrency = parseFloat(
        unitPrice * exchangeRate * quantity
      ).toFixed(2);

      setFieldValue(
        `items[${index}].total_price_in_shop_currency`,
        totalPriceInShopCurrency
      );
      setFieldValue(`items[${index}].total_price`, totalPrice);
    });

    const shipping_charge_supplier_currency =
      values.shipping_currency == values.shop_currency
        ? values.shipping_charge / (parseFloat(values.exchange_rate) || 1.0)
        : values.shipping_charge;
    const shipping_charge_in_shop_currency_calc =
      values.shipping_currency == values.shop_currency
        ? values.shipping_charge
        : values.shipping_charge * (parseFloat(values.exchange_rate) || 1.0);
    const transaction_charge_in_supplier_currency =
      values.transaction_fee_currency == values.shop_currency
        ? values.transaction_fee / (parseFloat(values.exchange_rate) || 1.0)
        : values.transaction_fee;
    const transaction_charge_in_shop_currency =
      values.transaction_fee_currency == values.shop_currency
        ? values.transaction_fee
        : values.transaction_fee * (parseFloat(values.exchange_rate) || 1.0);
    const other_expense_in_supplier_currency =
      values.other_expense_currency == values.shop_currency
        ? values.other_expense / (parseFloat(values.exchange_rate) || 1.0)
        : values.other_expense;
    const other_expense_in_shop_currency_calc =
      values.other_expense_currency == values.shop_currency
        ? values.other_expense
        : values.other_expense * (parseFloat(values.exchange_rate) || 1.0);

    const subTotal = values.items.reduce((sum, item) => {
      const totalPrice = parseFloat(item.total_price) || 0;
      return sum + totalPrice;
    }, 0);

    setFieldValue("sub_total", parseFloat(subTotal).toFixed(2));
    const subTotalInShopCurrency = values.items.reduce((sum, item) => {
      const totalPrice =
        parseFloat(
          item.total_price * (parseFloat(values.exchange_rate) || 1.0)
        ) || 0;
      return sum + totalPrice;
    }, 0);

    setFieldValue(
      "sub_total_in_shop_currency",
      parseFloat(subTotalInShopCurrency).toFixed(2)
    );

    setFieldValue(
      "shipping_charge_in_shop_currency",
      parseFloat(shipping_charge_in_shop_currency_calc).toFixed(2)
    );

    setFieldValue(
      "transaction_fee_in_shop_currency",
      parseFloat(transaction_charge_in_shop_currency).toFixed(2)
    );

    setFieldValue(
      "other_expense_in_shop_currency",
      parseFloat(other_expense_in_shop_currency_calc).toFixed(2)
    );

    const totalAmount =
      parseFloat(subTotal) +
      parseFloat(shipping_charge_supplier_currency) +
      parseFloat(transaction_charge_in_supplier_currency) +
      parseFloat(other_expense_in_supplier_currency);
    setFieldValue("total_amount", parseFloat(totalAmount).toFixed(2));

    const totalAmountInShopCurrency =
      parseFloat(subTotalInShopCurrency) +
      parseFloat(shipping_charge_in_shop_currency_calc) +
      parseFloat(transaction_charge_in_shop_currency) +
      parseFloat(other_expense_in_shop_currency_calc);
    setFieldValue(
      "total_amount_in_shop_currency",
      parseFloat(totalAmountInShopCurrency).toFixed(2)
    );
  }, [
    values.items,
    values.shipping_charge,
    values.exchange_rate,
    values.shipping_currency,
    values.transaction_fee_currency,
    values.transaction_fee,
    values.other_expense_currency,
    values.other_expense,
    setFieldValue,
    values.shop_currency,
  ]);

  return null;
}

export default function CreatePurchaseInvoice() {
  const navigate = useNavigate();
  const pageRef = useRef(null);
  const activePage = "Purchase Invoice";
  const linkHref = "/dashboard";
  // State to track if there are unsaved changes
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const shopCurrency = useSelector(
    (state) => state.authState.shop_info?.currency_code
  );

   const initialValues = {
    branch_id: "",
    date: formattedCurrentDate,
    time: formattedCurrentTime,
    supplier_id: "",
    supplier_invoice_id: "",
    supplier_invoice_number: "",
    currency: "",
    items: [],
    exchange_rate: "",
    sub_total: 0.0,
    sub_total_in_shop_currency: 0.0,
    shipping_charge: 0.0,
    invoice_type: "",
    total_amount: 0.0,
    total_amount_in_shop_currency: 0.0,
    shipping_charge_in_shop_currency: 0.0,
    shipping_currency: "",
    transaction_fee: 0.0,
    transaction_fee_currency: "",
    transaction_fee_in_shop_currency: 0.0,
    other_expense: 0.0,
    other_expense_currency: "",
    other_expense_in_shop_currency: 0.0,
    shop_currency: shopCurrency,
  };

  function CheckUnsavedChanges() {
  const { values } = useFormikContext();
    useEffect(() => {
    const hasChanges = values.items.length > 0;
    setHasUnsavedChanges(hasChanges)
    }, [values])
  };


  const { showSimpleConfirm } = useConfirm({
      title: 'Leave Page?',
      text: 'Are you sure you want to leave this page, you have unsaved data, save it as draft if you want to complete it later?',
      confirmButtonText: 'Leave page!',
      cancelButtonText: 'Cancel'
    });

useEffect(() => {
  const handleClickOutside = async (event) => {
    if (pageRef.current && !pageRef.current.contains(event.target)) {
      if (hasUnsavedChanges) {
        const target = event.target.closest('a[href]');

        // Prevent default behavior first to stop navigation
        event.preventDefault();
        event.stopPropagation();

        const confirmLeave = await showSimpleConfirm()
        if (confirmLeave) {
          if (target && target.href) {
            // Navigate to the link's href programmatically
            window.location.href = target.href;
          }
          // If not a link, do nothing (e.g., for other clicks, let browser handle)
        }
        // If user cancels, do nothing (navigation already prevented)
      }
    }
  };

  const handleBeforeUnload = (event) => {
    if (hasUnsavedChanges) {
      event.preventDefault();
      event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
    }
  };

  document.addEventListener('click', handleClickOutside); // Use 'click' instead of 'mousedown'
  window.addEventListener('beforeunload', handleBeforeUnload);

  return () => {
    document.removeEventListener('click', handleClickOutside);
    window.removeEventListener('beforeunload', handleBeforeUnload);
  };
}, [hasUnsavedChanges, showSimpleConfirm]);

  const [barcodeInput, setBarcodeInput] = useState("");
  const [selectedBranchId, setSelectedBranchId] = useState(null);
  const [selectedSupplierId, setSelectedSupplierId] = useState(null);
  const [selectedSupplierInvoiceData, setSelectedSupplierInvoiceData] = useState([]);
  const [isDraft, setAsDraft] = useState(false);

  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));

  const { data: supplierData } = useListAllSupplierQuery({
    branch_id: parseInt(selectedBranchId),
  });
  const supplierList = useMemo(() => {
    if (!supplierData?.data?.length) {
      return [];
    }
    return supplierData.data.map((values) => ({
      value: values.id,
      label: values.supplier_name + " (" + values.supplier_code + ")",
    }));
  }, [supplierData?.data]);

   const { data: supplierInvoiceData } = useListAllSupplierInvoiceQuery(
    {supplier_id: selectedSupplierId},
    {skip: !selectedSupplierId});
    const supplierInvoiceList = supplierInvoiceData?.data || [];
    const supplierInvoiceAry = supplierInvoiceList.map((values) => ({
    value: values.id,
    label: values.supplier_invoice_number,
    data: values
  }));

  const [conversionCurrencyList, setConversionCurrencyList] = useState([]);
  const invoiceTypeData = useSelector(
    (state) => state.commonState.purchaseInvoiceTypes
  );
  const invoiceTypeList = useMemo(() => {
    if (!invoiceTypeData?.data?.length) {
      return [];
    }
    return invoiceTypeData.data.map((values) => ({
      value: values.invoice_type_id,
      label: values.invoice_type,
    }));
  }, [invoiceTypeData?.data]);

  const handleSupplierChange = (e, setFieldValue) => {
    setSelectedSupplierId(parseInt(e.target.value));
    const selectedSupplier = supplierData?.data?.find(
      (supplier) => parseInt(supplier.id) === parseInt(e.target.value)
    );
    if (selectedSupplier) {
      const supplierDetails = selectedSupplier;
      setFieldValue("currency", supplierDetails?.currency_code || "");
      const conversionCurrencyList = [
        {
          value: shopCurrency,
          label: shopCurrency,
        },
        {
          value: supplierDetails?.currency_code,
          label: supplierDetails?.currency_code,
        },
      ];
      setConversionCurrencyList(conversionCurrencyList);
      setFieldValue("shipping_currency", supplierDetails?.currency_code);
      setFieldValue("transaction_fee_currency", supplierDetails?.currency_code);
      setFieldValue("other_expense_currency", supplierDetails?.currency_code);
    } else {
      setFieldValue("currency", "");
      setConversionCurrencyList([]);
    }
  };

  const handleSupplierInvoiceSelection = (e) => {
    const selectedSupplierInvoice = supplierInvoiceAry.find(
      (supplierInvoice) => parseInt(supplierInvoice.value) === parseInt(e.target.value)
    );
    if (selectedSupplierInvoice) {
      setSelectedSupplierInvoiceData(selectedSupplierInvoice.data);
    }
  };

  const { data: productData, isLoading: isProductLoading } = useGetAllProductdetailListQuery(
    { bar_code: barcodeInput },
    { skip: !barcodeInput }
  );

  const handleBarcodeSubmit = (push, values) => {
    if (!barcodeInput) {
      handleToast("error", "Please enter a barcode");
      return;
    }

    const isDuplicate = values.items.some(
      (item) => item.barcode === barcodeInput
    );
    if (isDuplicate) {
      handleToast("error", "This item has already been added");
      setBarcodeInput("");
      return;
    }

    if (productData?.data) {
      const product = productData.data[0];
      
      push({
        product_id: product.product_id,
        product_detail_id: product.id,
        description: product.product_name + " - " + product.sku,
        unit_price: "",
        quantity: "1",
        total_price: "",
        total_price_in_shop_currency: "",
        barcode: barcodeInput,
      });
      setBarcodeInput("");
    } else if (!isProductLoading) {
      handleToast("error", "No product found with this barcode");
      setBarcodeInput("");
    }
  };

  const [handleCreatePurchaseInvoiceApi, { isLoading }] =
    useCreatePurchaseInvoiceMutation();
  const handleSubmit = async (body) => {
    try {
      let itemsValidationFailed = false;
      let itemsDescriptionValidationFailed = false;
      let itemsUnitPriceValidationFailed = false;
      let itemsQuantityValidationFailed = false;
      body.items.forEach((item) => {
        itemsDescriptionValidationFailed =
          item.description == "" ? true : false;
        itemsUnitPriceValidationFailed = item.unit_price == "" ? true : false;
        itemsQuantityValidationFailed = item.quantity == "" ? true : false;
        if (
          itemsDescriptionValidationFailed ||
          itemsUnitPriceValidationFailed ||
          itemsQuantityValidationFailed
        ) {
          handleToast(
            "error",
            "Please fill all the required fields in items section"
          );
          itemsValidationFailed = true;
          return false;
        }
      });

      if (itemsValidationFailed) {
        return false;
      }

      const product_detail_id = [];
      const product_id = [];
      const item_sl_no = [];
      const item_description = [];
      const item_unit_price = [];
      const item_quantity = [];
      const item_total_price_from = [];
      const item_total_price_to = [];
      body.items.forEach((item, index) => {
        product_detail_id.push(item.product_detail_id),
        product_id.push(item.product_id),
        item_sl_no.push(index + 1);
        item_description.push(item.description);
        item_unit_price.push(parseFloat(item.unit_price));
        item_quantity.push(parseInt(item.quantity));
        item_total_price_from.push(parseFloat(item.total_price));
        item_total_price_to.push(parseFloat(item.total_price_in_shop_currency));
      });

      const createBody = {
        branch_id: parseInt(body.branch_id),
        supplier_id: parseInt(body.supplier_id),
        supplier_invoice_id: parseInt(body.supplier_invoice_id),
        supplier_invoice_number: body.supplier_invoice_number,
        invoice_type: parseInt(body.invoice_type),
        from_currency_code: body.currency,
        to_currency_code: shopCurrency,
        exchange_rate: parseFloat(body.exchange_rate),
        date: body.date,
        time: body.time,

        item_sl_no,
        product_id,
        product_detail_id,
        item_description,
        item_unit_price,
        item_quantity,
        item_total_price_from,
        item_total_price_to,

        sub_total_from: parseFloat(body.sub_total),
        sub_total_to: parseFloat(body.sub_total_in_shop_currency),

        shipping_charge_from_currency_code: body.shipping_currency,
        shipping_charge_to_currency_code: shopCurrency,
        shipping_charge_from: parseFloat(body.shipping_charge),
        shipping_charge_to: parseFloat(body.shipping_charge_in_shop_currency),

        transaction_fee_from_currency_code: body.transaction_fee_currency,
        transaction_fee_to_currency_code: shopCurrency,
        transaction_fee_from: parseFloat(body.transaction_fee),
        transaction_fee_to: parseFloat(body.transaction_fee_in_shop_currency),

        other_expense_charges_from_currency_code: body.other_expense_currency,
        other_expense_charges_to_currency_code: shopCurrency,
        other_expense_charges_from: parseFloat(body.other_expense),
        other_expense_charges_to: parseFloat(
          body.other_expense_in_shop_currency
        ),

        total_from: parseFloat(body.total_amount),
        total_to: parseFloat(body.total_amount_in_shop_currency),

        status: isDraft ? 1 : 3,
      };
      const resp = await handleCreatePurchaseInvoiceApi(createBody).unwrap();
      handleApiSuccess(resp);
      navigate("/purchaseInvoice");
    } catch (error) {
      handleApiErrors(error);
    }
  };

  if (isLoading) return <WebLoader />;

  return (
    <div className="page-wrapper" >
      <TopBar />
      <div className="body-wrapper" ref={pageRef}>
        <div className="container-fluid" style={{ padding: "30px" }}>
          <Breadcrumb activePage={activePage} linkHref={linkHref} />
          <div className="card">
            <div className="card-body">
              <div className="tab-content" id="pills-tabContent">
                <div
                  className="tab-pane fade show active"
                  id="pills-account"
                  role="tabpanel"
                  aria-labelledby="pills-account-tab"
                  tabIndex="0"
                >
                  <div className="row">
                    <div className="col-lg-12 d-flex align-items-stretch">
                      <div className="card w-100 border position-relative overflow-hidden mb-0">
                        <div className="card-body p-4">
                          <h4 className="card-title">Purchase Invoice</h4>
                          <p className="card-subtitle mb-4">
                            To create Purchase Invoice, add details and save from here
                          </p>
                          <Formik
                            initialValues={initialValues}
                            validationSchema={validation}
                            onSubmit={handleSubmit}
                          >
                            {({ values, setFieldValue }) => {
                              return (
                                <>
                                  <CheckUnsavedChanges />
                                  <TotalsCalculator />
                                  <Form
                                    name="invoice-create"
                                    className="needs-validation"
                                    autoComplete="off"
                                    encType="multipart/form-data"
                                  >
                                    <input
                                      type="hidden"
                                      name="shop_currency"
                                      id="shop_currency"
                                    />
                                    <div className="row">
                                      <div className="col-lg-4">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="branch_id"
                                            className="form-label"
                                          >
                                            Branch{" "}
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            name="branch_id"
                                            id="branch_id"
                                            className="form-select"
                                            type="select"
                                            options={branchesList}
                                            onChange={(e) => {
                                              setFieldValue("branch_id", e.target.value);
                                              setSelectedBranchId(e.target.value);
                                            }}
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-2"></div>
                                      <div className="col-lg-2">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="date"
                                            className="form-label"
                                          >
                                            Date{" "}
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            name="date"
                                            id="date"
                                            className="form-control"
                                            type="date"
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-2">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="time"
                                            className="form-label"
                                          >
                                            Time{" "}
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            name="time"
                                            id="time"
                                            className="form-control"
                                            type="time"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                    <div className="row">
                                      <div className="col-lg-4">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="supplier_id"
                                            className="form-label"
                                          >
                                            Supplier{" "}
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            name="supplier_id"
                                            id="supplier_id"
                                            className="form-select"
                                            type="select"
                                            options={supplierList}
                                            onChange={(e) => {
                                              setFieldValue("supplier_id", e.target.value);
                                              handleSupplierChange(e, setFieldValue);
                                            }}
                                          />
                                        </div>
                                      </div>
                                      <div className="col-lg-2"></div>
                                      {values.currency != "" && (
                                        <div className="col-lg-2">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="currency"
                                              className="form-label"
                                            >
                                              Currency
                                            </label>
                                            <FormikField
                                              name="currency"
                                              id="currency"
                                              className="form-control"
                                              type="text"
                                              disabled
                                            />
                                          </div>
                                        </div>
                                      )}
                                      {values.currency != "" && (
                                        <div className="col-lg-2">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="exchange_rate"
                                              className="form-label"
                                            >
                                              Exchange Rate ({shopCurrency})
                                              <span className="un-validation">(*)</span>
                                            </label>
                                            <FormikField
                                              name="exchange_rate"
                                              id="exchange_rate"
                                              className="form-control"
                                              type="number"
                                              step="0.01"
                                              min="0.0"
                                              placeholder="Exchange Rate"
                                              value={values.exchange_rate}
                                            />
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                    <div className="row">
                                      <div className="col-lg-4">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="invoice_type"
                                            className="form-label"
                                          >
                                            Invoice Type{" "}
                                            <span className="un-validation">(*)</span>
                                          </label>
                                          <FormikField
                                            name="invoice_type"
                                            id="invoice_type"
                                            className="form-select"
                                            type="select"
                                            options={invoiceTypeList}
                                          />
                                        </div>
                                      </div>
                                      </div>
                                      <div className="row">
                                      <div className="col-lg-4">
                                        <div className="mb-3">
                                          <label
                                            htmlFor="supplier_invoice_id"
                                            className="form-label"
                                          >
                                            Supplier Invoice{" "}
                                          </label>
                                          <FormikField
                                            name="supplier_invoice_id"
                                            id="supplier_invoice_id"
                                            className="form-select"
                                            type="select"
                                            options={supplierInvoiceAry}
                                            onChange={(e) =>
                                                            handleSupplierInvoiceSelection(e)
                                                      }
                                          />
                                        </div>
                                      </div>
                                      {selectedSupplierInvoiceData?.id &&
                                        <>
                                          <div className="row g-3 align-items-start">
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Exchange Rate</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.exchange_rate_with_currency || 'N/A'}</p>
                                            </div>
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Invoice Type</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.invoice_type_name || 'N/A'}</p>
                                            </div>
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Total (From)</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.total_from_with_currency || 'N/A'}</p>
                                            </div>
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Total (To)</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.total_to_with_currency || 'N/A'}</p>
                                            </div>
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Shipping Charges (From)</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.from_currency_code + " " + selectedSupplierInvoiceData?.shipping_charge_from || 'N/A'}</p>
                                            </div>
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Shipping Charges (To)</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.toCurrency_code + " " + selectedSupplierInvoiceData?.shipping_charge_to || 'N/A'}</p>
                                            </div>
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Transaction Charges (From)</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.from_currency_code + " " + selectedSupplierInvoiceData?.shipping_charge_from || 'N/A'}</p>
                                            </div>
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Transaction Charges (To)</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.toCurrency_code + " " + selectedSupplierInvoiceData?.shipping_charge_to || 'N/A'}</p>
                                            </div>
                                            <div className="col-md-2 col-sm-4 col-6">
                                              <label className="form-label fw-bold text-muted">Date</label>
                                              <p className="mb-0 text-dark">{selectedSupplierInvoiceData?.date || 'N/A'}</p>
                                            </div>
                                          </div>
                                        </>
                                      }
                                      </div>
                                    {values.exchange_rate && (
                                      <>
                                        <hr />
                                        <h4 className="card-title">Enter Item Details</h4>
                                        <div className="row">
                                          <div className="card-body">
                                            <FieldArray name="items">
                                              {({ push, remove }) => (
                                                <>
                                                  <div className="row mb-3">
                                                    <div className="col-xl-6 col-lg-8 col-md-8 col-sm-12">
                                                      <div className="input-group">
                                                        <input
                                                          type="text"
                                                          className="form-control"
                                                          placeholder="Enter Barcode"
                                                          name="barcode"
                                                          value={barcodeInput}
                                                          onChange={(e) =>
                                                            setBarcodeInput(e.target.value)
                                                          }
                                                        />
                                                        <button
                                                          className="btn btn-primary"
                                                          type="button"
                                                          onClick={() =>
                                                            handleBarcodeSubmit(push, values)
                                                          }
                                                        >
                                                          <i className="ti ti-circle-plus fs-4 me-1"></i>Add Item
                                                        </button>
                                                      </div>
                                                    </div>
                                                  </div>
                                                  {values.items.length > 0 && (
                                                    <>
                                                    <hr></hr>
                                                      <div className="row">
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6">
                                                          <label className="form-label">
                                                            Barcode{" "}
                                                          </label>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6">
                                                          <label className="form-label">
                                                            Item Name{" "}
                                                          </label>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6 align-left">
                                                          <label className="form-label">
                                                            Unit Price ({values.currency}){" "}
                                                            <span className="un-validation">(*)</span>
                                                          </label>
                                                        </div>
                                                        <div className="col-xl-1 col-lg-1 col-md-4 col-sm-6 align-center">
                                                          <label className="form-label">
                                                            Qty <span className="un-validation">(*)</span>
                                                          </label>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6 align-center">
                                                          <label className="form-label">
                                                            Total Price ({values.currency})
                                                          </label>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6 align-center">
                                                          <label className="form-label">
                                                            Total Price ({shopCurrency})
                                                          </label>
                                                        </div>
                                                      </div>
                                                      <hr />
                                                    </>
                                                  )}
                                                  <div id="fields" className="my-4">
                                                    {values.items.map((item, index) => (
                                                      <div className="row" key={index}>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6">
                                                          <div className="mb-3">
                                                            <FormikField
                                                              type="text"
                                                              name={`items[${index}].barcode`}
                                                              id={`items[${index}].barcode`}
                                                              placeholder="Barcode *"
                                                              autoComplete="off"
                                                              className="form-control"
                                                              value={values.items[index].barcode}
                                                              readOnly= {true}
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6">
                                                          <div className="mb-3">
                                                            <FormikField
                                                              type="text"
                                                              name={`items[${index}].description`}
                                                              id={`items[${index}].description`}
                                                              placeholder="Item Name *"
                                                              autoComplete="off"
                                                              className="form-control"
                                                              value={values.items[index].description}
                                                              readOnly= {true}
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6">
                                                          <div className="mb-3">
                                                            <FormikField
                                                              type="number"
                                                              name={`items[${index}].unit_price`}
                                                              id={`items[${index}].unit_price`}
                                                              placeholder={`Price ${values.currency} *`}
                                                              autoComplete="off"
                                                              className="form-control"
                                                              step="0.01"
                                                              min="0"
                                                              value={values.items[index].unit_price}
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-1 col-lg-1 col-md-4 col-sm-6">
                                                          <div className="mb-3">
                                                            <FormikField
                                                              type="number"
                                                              name={`items[${index}].quantity`}
                                                              id={`items[${index}].quantity`}
                                                              placeholder="Qty *"
                                                              autoComplete="off"
                                                              className="form-control"
                                                              min="1"
                                                              value={values.items[index].quantity}
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6">
                                                          <div className="mb-3">
                                                            <FormikField
                                                              type="number"
                                                              name={`items[${index}].total_price`}
                                                              id={`items[${index}].total_price`}
                                                              placeholder={`Total (${values.currency})`}
                                                              autoComplete="off"
                                                              className="form-control"
                                                              step="0.01"
                                                              readOnly={true}
                                                              value={values.items[index].total_price}
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-2 col-md-4 col-sm-6">
                                                          <div className="mb-3">
                                                            <FormikField
                                                              type="number"
                                                              name={`items[${index}].total_price_in_shop_currency`}
                                                              id={`items[${index}].total_price_in_shop_currency`}
                                                              placeholder={`Total (${shopCurrency})`}
                                                              autoComplete="off"
                                                              className="form-control"
                                                              step="0.01"
                                                              readOnly={true}
                                                              value={
                                                                values.items[index]
                                                                  .total_price_in_shop_currency
                                                              }
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-1">
                                                          <div className="mb-3">
                                                            <button
                                                              type="button"
                                                              className="btn btn-danger fw-medium"
                                                              onClick={() => remove(index)}
                                                            >
                                                              <i className="ti ti-circle-minus fs-5 d-flex"></i>
                                                            </button>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    ))}
                                                  </div>
                                                  <hr />
                                                  <div className="d-flex justify-content-end">
                                                    <div className="col-lg-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="shipping_currency"
                                                          className="form-label"
                                                        >
                                                          Shipping Currency
                                                        </label>
                                                        <FormikField
                                                          name="shipping_currency"
                                                          id="shipping_currency"
                                                          className="form-select"
                                                          type="select"
                                                          options={conversionCurrencyList}
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-2 ms-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="shipping_charge"
                                                          className="form-label"
                                                        >
                                                          Shipping Charge
                                                          {values.shipping_currency
                                                            ? ` (${values.shipping_currency})`
                                                            : ""}
                                                        </label>
                                                        <FormikField
                                                          name="shipping_charge"
                                                          id="shipping_charge"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          placeholder={`Shipping Price in ${values.shipping_currency}`}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <div className="d-flex justify-content-end">
                                                    <div className="col-lg-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="transaction_fee_currency"
                                                          className="form-label"
                                                        >
                                                          Transaction Fee Currency
                                                        </label>
                                                        <FormikField
                                                          name="transaction_fee_currency"
                                                          id="transaction_fee_currency"
                                                          className="form-select"
                                                          type="select"
                                                          options={conversionCurrencyList}
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-2 ms-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="transaction_fee"
                                                          className="form-label"
                                                        >
                                                          Transaction Fee
                                                          {values.transaction_fee_currency
                                                            ? ` (${values.transaction_fee_currency})`
                                                            : ""}
                                                        </label>
                                                        <FormikField
                                                          name="transaction_fee"
                                                          id="transaction_fee"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          placeholder={`Transaction Fee in ${values.transaction_fee_currency}`}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <div className="d-flex justify-content-end">
                                                    <div className="col-lg-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="other_expense_currency"
                                                          className="form-label"
                                                        >
                                                          Other Expense Currency
                                                        </label>
                                                        <FormikField
                                                          name="other_expense_currency"
                                                          id="other_expense_currency"
                                                          className="form-select"
                                                          type="select"
                                                          options={conversionCurrencyList}
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-2 ms-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="other_expense"
                                                          className="form-label"
                                                        >
                                                          Other Expense
                                                          {values.other_expense_currency
                                                            ? ` (${values.other_expense_currency})`
                                                            : ""}
                                                        </label>
                                                        <FormikField
                                                          name="other_expense"
                                                          id="other_expense"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          placeholder={`Other Expense in ${values.other_expense_currency}`}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <hr />
                                                  <div className="d-flex justify-content-end">
                                                    <div className="col-lg-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="sub_total"
                                                          className="form-label"
                                                        >
                                                          Sub Total ({values.currency})
                                                        </label>
                                                        <FormikField
                                                          name="sub_total"
                                                          id="sub_total"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          readOnly={true}
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-2 ms-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="sub_total_in_shop_currency"
                                                          className="form-label"
                                                        >
                                                          Sub Total ({shopCurrency})
                                                        </label>
                                                        <FormikField
                                                          name="sub_total_in_shop_currency"
                                                          id="sub_total_in_shop_currency"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          readOnly={true}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <div className="d-flex justify-content-end">
                                                    <div className="col-lg-4 ms-1">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="shipping_charge_in_shop_currency"
                                                          className="form-label"
                                                        >
                                                          Shipping Charge ({shopCurrency})
                                                        </label>
                                                        <FormikField
                                                          name="shipping_charge_in_shop_currency"
                                                          id="shipping_charge_in_shop_currency"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          readOnly={true}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <div className="d-flex justify-content-end">
                                                    <div className="col-lg-4 ms-1">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="transaction_fee_in_shop_currency"
                                                          className="form-label"
                                                        >
                                                          Transaction Fee ({shopCurrency})
                                                        </label>
                                                        <FormikField
                                                          name="transaction_fee_in_shop_currency"
                                                          id="transaction_fee_in_shop_currency"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          readOnly={true}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <div className="d-flex justify-content-end">
                                                    <div className="col-lg-4 ms-1">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="other_expense_in_shop_currency"
                                                          className="form-label"
                                                        >
                                                          Other Expense ({shopCurrency})
                                                        </label>
                                                        <FormikField
                                                          name="other_expense_in_shop_currency"
                                                          id="other_expense_in_shop_currency"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          readOnly={true}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <hr />
                                                  <div className="d-flex justify-content-end">
                                                    <div className="col-lg-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="total_amount"
                                                          className="form-label"
                                                        >
                                                          Total Amount ({values.currency})
                                                        </label>
                                                        <FormikField
                                                          name="total_amount"
                                                          id="total_amount"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          readOnly={true}
                                                        />
                                                      </div>
                                                    </div>
                                                    <div className="col-lg-2 ms-2">
                                                      <div className="mb-3">
                                                        <label
                                                          htmlFor="total_amount_in_shop_currency"
                                                          className="form-label"
                                                        >
                                                          Total Amount ({shopCurrency})
                                                        </label>
                                                        <FormikField
                                                          name="total_amount_in_shop_currency"
                                                          id="total_amount_in_shop_currency"
                                                          className="form-control"
                                                          type="number"
                                                          step="0.01"
                                                          min="0"
                                                          readOnly={true}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                </>
                                              )}
                                            </FieldArray>
                                          </div>
                                        </div>
                                        <div className="col-12">
                                          <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                            <button
                                              className="btn btn-warning"
                                              type="submit" onClick={
                                                        setAsDraft(true)
                                                      }
                                            >
                                              Save As Draft
                                            </button>
                                            <button
                                              className="btn btn-primary"
                                              type="submit"
                                            >
                                              Create Invoice
                                            </button>
                                          </div>
                                        </div>
                                      </>
                                    )}
                                  </Form>
                                </>
                              );
                            }}
                          </Formik>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}