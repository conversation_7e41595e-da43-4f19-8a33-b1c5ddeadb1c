import TopBar from "../../../components/layout/topBar";
import Breadcrumb from "../../../components/breadcrumb";
import { Link, useNavigate } from "react-router-dom";
import {
  useListAllSupplierQuery,
} from "../../../feature/api/supplierDataApiSlice";
import { Table } from "../../../components/datatable";
import { handleApiSuccess } from "../../../hooks/handleApiSucess";
import { handleApiErrors } from "../../../hooks/handleApiErrors";
import { useMemo, useState } from "react";
import WebLoader from "../../../components/webLoader";
import { PaginationComponent } from "../../../components/pagination";
import useConfirm from "../../../hooks/useConfirm";
import { useGetShopBranchsQuery } from "../../../feature/api/branchDataApiSlice";
import { useSelector } from "react-redux";
import { useDeleteSupplierInvoiceMutation, useListSupplierInvoiceQuery } from "../../../feature/api/supplierInvoiceDataApiSlice";

export default function PaymentToSupplierInvoice() {
  const activePage = "Purchase Invoice";
  const linkHref = "/dashboard";
  const navigation = useNavigate();
  /* **************** Start list all Suppliers ******************* */
  const [currentPage, setCurrentPage] = useState(1);
  const [filterSupplier, setFilterSupplier] = useState(null);
  const [filterBranchId, setFilterBranchId] = useState(null);
  const [filterInvoiceType, setFilterInvoiceType] = useState(null);
  const [filterInvoiceStatus, setFilterInvoiceStatus] = useState(null);
  const [filterKeywords, setFilterKeywords] = useState("");
  const [filterDate, setFilterDate] = useState("");
  const { data: paymentToSupplierInvoiceListResp, isLoading } =
    useListSupplierInvoiceQuery({
      page: currentPage,
      supplier_id: parseInt(filterSupplier),
      branch_id: parseInt(filterBranchId),
      invoice_type: parseInt(filterInvoiceType),
      status: parseInt(filterInvoiceStatus),
      date: filterDate,
      keywords: filterKeywords,
    });
  const paymentToSupplierInvoiceList = useMemo(() => {
    if (!paymentToSupplierInvoiceListResp?.data?.list?.length) {
      if (currentPage > 1) setCurrentPage((current) => current - 1);
      return [];
    }
    return paymentToSupplierInvoiceListResp?.data?.list;
  }, [currentPage, paymentToSupplierInvoiceListResp?.data?.list]);
  const pageData = useMemo(
    () => paymentToSupplierInvoiceListResp?.data?.page ?? null,
    [paymentToSupplierInvoiceListResp]
  );

  /* **************** End list all Suppliers ******************* */

  /* ****************  Start Filter ****************** */
  const handleSupplierFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterSupplier(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleBranchFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterBranchId(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleInvoiceTypeFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterInvoiceType(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleInvoiceStatusFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterInvoiceStatus(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleDateFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterDate(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };
  const handleKeywordsFilter = (event) => {
    try {
      const selectedValue = event.target.value;
      setFilterKeywords(selectedValue);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** Start list all invoice types ******************* */
  const invoiceTypes = useSelector((state) => state.commonState.invoiceTypes);
  /* **************** End list all invoice types ******************* */

  /* **************** Start list all invoice statuses ******************* */
    const invoiceStatusData = useSelector(
    (state) => state.commonState.invoiceStatus
  );
  const invoiceStatusList = useMemo(() => {
    if (!invoiceStatusData?.data?.length) {
      return [];
    }
    return invoiceStatusData.data.map((values) => ({
      value: values.id,
      label: values.status,
    }));
  }, [invoiceStatusData?.data]);
  /* **************** Start list all invoice statuses ******************* */

  /* **************** Start list suppliers ******************* */
  const userSupplierData = useListAllSupplierQuery();
  const userSupplierDataList = userSupplierData?.data?.data || [];
  const userSupplierList = userSupplierDataList.map((values) => ({
    value: values.id,
    label: values.supplier_name + " (" + values.supplier_code + ")",
  }));
  /* **************** End list suppliers ******************* */

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** End Filter ***************** */

  /* **************** Start Paginatation ***************** */
  const fetchData = async (page) => {
    try {
      setCurrentPage(page); // Update the page state
    } catch (error) {
      handleApiErrors(error);
    }
  };
  /* **************** End Paginatation ***************** */

  const { showSimpleConfirm } = useConfirm({
    title: "Delete Supplier Invoice?",
    text: "Are you sure you want to delete this supplier invoice?",
    confirmButtonText: "Yes, delete supplier invoice!",
    cancelButtonText: "Cancel",
  });
  const [handleDeleteSupplierInvoiceApi, { isLoading: isDeleteLoading }] =
    useDeleteSupplierInvoiceMutation();
  const onDeleteSupplierInvoiceHandler = async (id) => {
    const confirmed = await showSimpleConfirm();
    if (confirmed) {
      try {
        const body = { id: id };
        const resp = await handleDeleteSupplierInvoiceApi(body).unwrap();
        handleApiSuccess(resp);
        navigation("/paymentToSupplierInvoice"); // Redirect to the desired page
      } catch (error) {
        handleApiErrors(error);
      }
    }
  };

  const onEditSupplierInvoiceDetailsHandler = (d) => {
    navigation(`/editPaymentToSupplierInvoice/${d.id}`);
  };

  const invoicePreviewHandler = (d) => {
    navigation(`/previewSupplierInvoice/${d.id}`);
  };

  /* **************** Web Loader  ******************* */
  if (isLoading || isDeleteLoading) return <WebLoader />;
  /* **************** End Web Loader  ******************* */

  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid mw-100">
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card w-100 position-relative overflow-hidden">
              <div className="px-4 py-3 border-bottom">
                <div className="d-sm-flex align-items-center justify-space-between">
                  <h4 className="card-title mb-0">
                    Purchase Invoice
                  </h4>
                  <nav aria-label="breadcrumb" className="ms-auto">
                    <ol className="breadcrumb">
                      <li className="breadcrumb-item" aria-current="page">
                        <Link
                          type="button"
                          to={"/createPaymentToPurchaseInvoice"}
                          className="btn btn-primary"
                        >
                          Create New Purchase Invoice
                        </Link>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="table-responsive">
                  <div className="d-flex justify-content-between align-items-center gap-6 mb-9">
                    <div className="d-flex gap-6">
                      <div>
                        <select
                          value={filterBranchId}
                          className="form-control search-chat py-2"
                          onChange={handleBranchFilter}
                        >
                          <option value="">All Branches</option>
                          {branchesList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterSupplier}
                          className="form-control search-chat py-2"
                          onChange={handleSupplierFilter}
                        >
                          <option value="">All Suppliers</option>
                          {userSupplierList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterInvoiceType}
                          className="form-control search-chat py-2"
                          onChange={handleInvoiceTypeFilter}
                        >
                          <option value="">All Invoice Types</option>
                          {invoiceTypes?.data?.map((option) => (
                            <option
                              key={option.invoice_type_id}
                              value={option.invoice_type_id}
                            >
                              {option.invoice_type}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <select
                          value={filterInvoiceStatus}
                          className="form-control search-chat py-2"
                          onChange={handleInvoiceStatusFilter}
                        >
                          <option value="">All Invoice Status</option>
                          {invoiceStatusList.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <input
                          type="date"
                          className="form-control search-chat py-2"
                          onChange={handleDateFilter}
                        />
                      </div>
                    </div>
                    <div className="position-relative">
                      <input
                        type="text"
                        className="form-control search-chat py-2 ps-5"
                        id="text-srh"
                        onChange={handleKeywordsFilter}
                        placeholder="Keyword Search..."
                        value={filterKeywords}
                      />
                      <i className="ti ti-search position-absolute top-50 start-0 translate-middle-y fs-6 text-dark ms-3"></i>
                    </div>
                  </div>
                  <Table
                    headCells={[
                      { key: "sel_id", label: "#", align: "left" },
                      {
                        key: "supplier_invoice_number",
                        label: "Invoice no.",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/editPaymentToSupplierInvoice/${row.id}`,
                        }),
                      },
                      {
                        key: "supplier_name",
                        label: "Supplier Name",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/viewSupplier/${row.supplier_id}`,
                        }),
                      },
                      {
                        key: "invoice_type_name",
                        label: "Invoice Type",
                        align: "left",
                      },
                      {
                        key: "branch_name",
                        label: "Branch",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/editBranch/${row.branch_id}`,
                        }),
                      },
                      {
                        key: "exchange_rate_with_currency",
                        label: "Exchange Rate",
                        align: "center",
                      },
                      {
                        key: "total_from_with_currency",
                        label: "Total From",
                        align: "center",
                      },
                      {
                        key: "total_to_with_currency",
                        label: "Total To",
                        align: "center",
                      },
                      {
                        key: "user_name",
                        label: "User",
                        align: "left",
                        linkTo: (row) => ({
                          to: `/userProfile/${row.user_id}`,
                        }),
                      },
                      {
                        key: "status_name",
                        key_id: "status",
                        label: "Status",
                        align: "center",
                      },
                    ]}
                    data={paymentToSupplierInvoiceList}
                    onDeleteHandler={onDeleteSupplierInvoiceHandler}
                    onEditHandler={onEditSupplierInvoiceDetailsHandler}
                    customActions={[
                      {
                        label: "Preview",
                        icon: "ti ti-eye",
                        handler: invoicePreviewHandler,
                      }
                    ]}
                  />
                  <PaginationComponent
                    totalCount={pageData?.total_count}
                    pageSize={pageData?.page_size}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    onPageChange={fetchData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
