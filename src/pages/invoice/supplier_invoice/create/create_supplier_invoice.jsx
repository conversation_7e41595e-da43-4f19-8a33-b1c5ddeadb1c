import TopBar from "../../../../components/layout/topBar";
import Breadcrumb from "../../../../components/breadcrumb";
import { handleApiErrors } from "../../../../hooks/handleApiErrors";
import { handleApiSuccess } from "../../../../hooks/handleApiSucess";
import { Formik, Form, FieldArray, useFormikContext } from "formik";
import * as yup from "yup";
import FormikField from "../../../../components/formikField";
import { useListAllSupplierQuery } from "../../../../feature/api/supplierDataApiSlice";
import { useNavigate } from "react-router-dom";
import WebLoader from "../../../../components/webLoader";
import { useGetShopBranchsQuery } from "../../../../feature/api/branchDataApiSlice";
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useCreateSupplierInvoiceMutation } from "../../../../feature/api/supplierInvoiceDataApiSlice";
import { handleToast } from "../../../../hooks/handleToast";
import { formatDate, formatTime } from "../../../../utils/dateHelpers";

/* **************** Start Date and Time formatting ******************* */

const formattedCurrentDate = formatDate(new Date().toLocaleDateString());
const formattedCurrentTime = formatTime(new Date().toLocaleTimeString());

/* **************** End Date and Time formatting ******************* */

const validation = yup.object().shape({
  branch_id: yup.string().required().label("Branch"),
  date: yup.string().required().label("Date"),
  time: yup.string().required().label("Time"),
  supplier_id: yup.string().required().label("Supplier"),
  currency: yup.string().label("Currency"),
  exchange_rate: yup.number().required().min(0.0).label("Exchange Rate"),
  shipping_charge: yup.number().min(0.0).label("Shipping Charge"),
  transaction_fee: yup.number().min(0.0).label("Transaction Fee"),
  // other_expense: yup.number().min(0.0).label("Other Expense"),
});

function TotalsCalculator() {
  const { values, setFieldValue } = useFormikContext();
  useEffect(() => {
    // Update total_price for each item based on current unit_price, quantity, and exchange_rate
    values.items.forEach((item, index) => {
      const unitPrice = parseFloat(item.unit_price) || 0;
      const quantity = parseFloat(item.quantity) || 0;
      const exchangeRate = parseFloat(values.exchange_rate) || 1.0;

      const totalPrice = parseFloat(unitPrice * quantity).toFixed(2);
      const totalPriceInShopCurrency = parseFloat(
        unitPrice * exchangeRate * quantity
      ).toFixed(2);

      setFieldValue(
        `items[${index}].total_price_in_shop_currency`,
        totalPriceInShopCurrency
      );
      setFieldValue(`items[${index}].total_price`, totalPrice);
    });

    const shipping_charge_supplier_currency =
      values.shipping_currency == values.shop_currency
        ? values.shipping_charge / (parseFloat(values.exchange_rate) || 1.0)
        : values.shipping_charge;
    const shipping_charge_in_shop_currency_calc =
      values.shipping_currency == values.shop_currency
        ? values.shipping_charge
        : values.shipping_charge * (parseFloat(values.exchange_rate) || 1.0);
    const transaction_charge_in_supplier_currency =
      values.transaction_fee_currency == values.shop_currency
        ? values.transaction_fee / (parseFloat(values.exchange_rate) || 1.0)
        : values.transaction_fee;
    const transaction_charge_in_shop_currency =
      values.transaction_fee_currency == values.shop_currency
        ? values.transaction_fee
        : values.transaction_fee * (parseFloat(values.exchange_rate) || 1.0);
    // const other_expense_in_supplier_currency =
    //   values.other_expense_currency == values.shop_currency
    //     ? values.other_expense / (parseFloat(values.exchange_rate) || 1.0)
    //     : values.other_expense;
    // const other_expense_in_shop_currency_calc =
    //   values.other_expense_currency == values.shop_currency
    //     ? values.other_expense
    //     : values.other_expense * (parseFloat(values.exchange_rate) || 1.0);

    // Calculate sub_total
    const subTotal = values.items.reduce((sum, item) => {
      const totalPrice = parseFloat(item.total_price) || 0;
      return sum + totalPrice;
    }, 0);

    setFieldValue("sub_total", parseFloat(subTotal).toFixed(2));
    const subTotalInShopCurrency = values.items.reduce((sum, item) => {
      const totalPrice =
        parseFloat(item.total_price * (parseFloat(values.exchange_rate) || 1.0)) || 0;
      return sum + totalPrice;
    }, 0);

    setFieldValue(
      "sub_total_in_shop_currency",
      parseFloat(subTotalInShopCurrency).toFixed(2)
    );

    setFieldValue(
      "shipping_charge_in_shop_currency",
      parseFloat(shipping_charge_in_shop_currency_calc).toFixed(2)
    );

    setFieldValue(
      "transaction_fee_in_shop_currency",
      parseFloat(transaction_charge_in_shop_currency).toFixed(2)
    );

    // setFieldValue(
    //   "other_expense_in_shop_currency",
    //   parseFloat(other_expense_in_shop_currency_calc).toFixed(2)
    // );

    // Calculate total amount
    const totalAmount =
      parseFloat(subTotal) +
      parseFloat(shipping_charge_supplier_currency) +
      parseFloat(transaction_charge_in_supplier_currency) 
      // +
      // parseFloat(other_expense_in_supplier_currency);
    setFieldValue("total_amount", parseFloat(totalAmount).toFixed(2));

    const totalAmountInShopCurrency =
      parseFloat(subTotalInShopCurrency) +
      parseFloat(shipping_charge_in_shop_currency_calc) +
      parseFloat(transaction_charge_in_shop_currency)
      //  +
      // parseFloat(other_expense_in_shop_currency_calc);
    setFieldValue(
      "total_amount_in_shop_currency",
      parseFloat(totalAmountInShopCurrency).toFixed(2)
    );
  }, [
    values.items,
    values.shipping_charge,
    values.exchange_rate,
    values.shipping_currency,
    values.transaction_fee_currency,
    values.transaction_fee,
    // values.other_expense_currency,
    // values.other_expense,
    setFieldValue,
    values.shop_currency,
  ]);

  return null;
}

export default function CreateSupplierInvoice() {
  const navigate = useNavigate();
  const activePage = "Supplier Invoice";
  const linkHref = "/dashboard";

  /* **************** Start Shop Info ******************* */
  const shopCurrency = useSelector(
    (state) => state.authState.shop_info?.currency_code
  );
  /* **************** End Shop Info ******************* */

  const initialValues = {
  branch_id: "",
  date: formattedCurrentDate,
  time: formattedCurrentTime,
  supplier_id: "",
  currency: "",
  items: [
    {
      id: "",
      description: "",
      unit_price: "",
      quantity: "",
      total_price: "",
      total_price_in_shop_currency: "",
    },
  ],
  exchange_rate: "",
  sub_total: 0.0,
  sub_total_in_shop_currency: 0.0,
  shipping_charge: 0.0,
  invoice_type: "",
  total_amount: 0.0,
  total_amount_in_shop_currency: 0.0,
  shipping_charge_in_shop_currency: 0.0,
  shipping_currency: "",
  transaction_fee: 0.0,
  transaction_fee_currency: "",
  transaction_fee_in_shop_currency: 0.0,
  // other_expense: 0.0,
  // other_expense_currency: "",
  // other_expense_in_shop_currency: 0.0,
  shop_currency: shopCurrency
};

  /* **************** Start Branch List ******************* */
  const branchListResp = useGetShopBranchsQuery();
  const branchList = branchListResp.data?.data || [];
  const branchesList = branchList.map((values) => ({
    value: values.id,
    label: values.branch_name + " (" + values.branch_type_name + ")",
  }));
  /* **************** End Branch List ******************* */

  /* **************** Start Handle Branch change and load suppliers in select box ******************* */

  const [selectedBranchId] = useState(null);
  const { data: supplierData } = useListAllSupplierQuery({
    branch_id: selectedBranchId,
  });
  const supplierList = useMemo(() => {
    if (!supplierData?.data?.length) {
      return [];
    }
    return supplierData.data.map((values) => ({
      value: values.id,
      label: values.supplier_name + " (" + values.supplier_code + ")",
    }));
  }, [supplierData?.data]);

  /* **************** End Handle Branch change and load suppliers in select box ******************* */

  /* **************** Start Handle Supplier change and load suppliers in select box ******************* */

  const [conversionCurrencyList, setConversionCurrencyList] = useState([]);
  const invoiceTypeData = useSelector(
    (state) => state.commonState.supplierInvoiceTypes
  );
  const invoiceTypeList = useMemo(() => {
    if (!invoiceTypeData?.data?.length) {
      return [];
    }
    return invoiceTypeData.data.map((values) => ({
      value: values.invoice_type_id,
      label: values.invoice_type,
    }));
  }, [invoiceTypeData?.data]);

  const handleSupplierChange = (e, setFieldValue) => {
    const selectedSupplier = supplierData.data.find(
      (supplier) => parseInt(supplier.id) === parseInt(e.target.value)
    );
    if (selectedSupplier) {
      const supplierDetails = selectedSupplier;
      setFieldValue("currency", supplierDetails?.currency_code || "");
      const conversionCurrencyList = [
        {
          value: shopCurrency,
          label: shopCurrency,
        },
        {
          value: supplierDetails?.currency_code,
          label: supplierDetails?.currency_code,
        },
      ];
      setConversionCurrencyList(conversionCurrencyList);
      setFieldValue("shipping_currency", supplierDetails?.currency_code);
      setFieldValue("transaction_fee_currency", supplierDetails?.currency_code);
      // setFieldValue("other_expense_currency", supplierDetails?.currency_code);
    } else {
      setFieldValue("currency", "");
    }
  };

  /* **************** End Handle Supplier change and load suppliers in select box ******************* */

  /* **************** Start Create Supplier Invoice ******************* */

  const [handleCreateSupplierApi, { isLoading }] =
    useCreateSupplierInvoiceMutation();
  const handleSubmit = async (body) => {
    try {
      // check items validation
      let itemsValidationFailed = false;
      let itemsDescriptionValidationFailed = false;
      let itemsUnitPriceValidationFailed = false;
      let itemsQuantityValidationFailed = false;
      body.items.forEach((item) => {
        itemsDescriptionValidationFailed =
          item.description == "" ? true : false;
        itemsUnitPriceValidationFailed = item.unit_price == "" ? true : false;
        itemsQuantityValidationFailed = item.quantity == "" ? true : false;
        if (
          itemsDescriptionValidationFailed ||
          itemsUnitPriceValidationFailed ||
          itemsQuantityValidationFailed
        ) {
          handleToast(
            "error",
            "Please fill all the required fields in items section"
          );
          itemsValidationFailed = true;
          return false;
        }
      });

      if (itemsValidationFailed) {
        return false;
      }

      const item_sl_no = [];
      const item_description = [];
      const item_unit_price = [];
      const item_quantity = [];
      const item_total_price_from = [];
      const item_total_price_to = [];
      body.items.forEach((item, index) => {
        item_sl_no.push(index + 1);
        item_description.push(item.description);
        item_unit_price.push(parseFloat(item.unit_price));
        item_quantity.push(parseInt(item.quantity));
        item_total_price_from.push(parseFloat(item.total_price));
        item_total_price_to.push(parseFloat(item.total_price_in_shop_currency));
      });

      const createBody = {
        branch_id: parseInt(body.branch_id),
        supplier_id: parseInt(body.supplier_id),
        invoice_type: parseInt(body.invoice_type),
        from_currency_code: body.currency,
        to_currency_code: shopCurrency,
        exchange_rate: parseFloat(body.exchange_rate),
        date: body.date,
        time: body.time,

        item_sl_no: item_sl_no,
        item_description: item_description,
        item_unit_price: item_unit_price,
        item_quantity: item_quantity,
        item_total_price_from: item_total_price_from,
        item_total_price_to: item_total_price_to,

        sub_total_from: parseFloat(body.sub_total),
        sub_total_to: parseFloat(body.sub_total_in_shop_currency),
        shipping_charge_from_currency_code: body.shipping_currency,
        shipping_charge_to_currency_code: shopCurrency,
        shipping_charge_from: parseFloat(body.shipping_charge),
        shipping_charge_to: parseFloat(body.shipping_charge_in_shop_currency),
        transaction_fee_from_currency_code: body.transaction_fee_currency,
        transaction_fee_to_currency_code: shopCurrency,
        transaction_fee_from: parseFloat(body.transaction_fee),
        transaction_fee_to: parseFloat(body.transaction_fee_in_shop_currency),
        // other_expense_charges_from_currency_code: body.other_expense_currency,
        // other_expense_charges_to_currency_code: shopCurrency,
        // other_expense_charges_from: parseFloat(body.other_expense),
        // other_expense_charges_to: parseFloat(
        //   body.other_expense_in_shop_currency
        // ),
        total_from: parseFloat(body.total_amount),
        total_to: parseFloat(body.total_amount_in_shop_currency),
      };
      const resp = await handleCreateSupplierApi(createBody).unwrap();
      handleApiSuccess(resp);
      navigate("/supplierInvoice");
    } catch (error) {
      handleApiErrors(error);
    }
  };

  /* **************** End Create Supplier Invoice ******************* */

  if (isLoading) return <WebLoader />;
  return (
    <>
      <div className="page-wrapper">
        <TopBar />
        <div className="body-wrapper">
          <div className="container-fluid" style={{ padding: "30px" }}>
            <Breadcrumb activePage={activePage} linkHref={linkHref} />
            <div className="card">
              <div className="card-body">
                <div className="tab-content" id="pills-tabContent">
                  <div
                    className="tab-pane fade show active"
                    id="pills-account"
                    role="tabpanel"
                    aria-labelledby="pills-account-tab"
                    tabIndex="0"
                  >
                    <div className="row">
                      <div className="col-lg-12 d-flex align-items-stretch">
                        <div className="card w-100 border position-relative overflow-hidden mb-0">
                          <div className="card-body p-4">
                            <h4 className="card-title">
                              Supplier Invoice
                            </h4>
                            <p className="card-subtitle mb-4">
                              To create Supplier Invoice, add details
                              and save from here
                            </p>
                            <Formik
                              initialValues={initialValues}
                              validationSchema={validation}
                              onSubmit={handleSubmit}
                            >
                              {({ values, setFieldValue }) => {
                                return (
                                  <>
                                    <TotalsCalculator/>
                                    <Form
                                      name="invoice-create"
                                      className="needs-validation"
                                      autoComplete="off"
                                      encType="multipart/form-data"
                                    >
                                      <input type="hidden" name="shop_currency" id="shop_currency"/>
                                      <div className="row">
                                        <div className="col-lg-6"></div>
                                      </div>
                                      <div className="row">
                                        <div className="col-lg-4 col-md-12">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="branch_id"
                                              className="form-label"
                                            >
                                              Branch{" "}
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="branch_id"
                                              id="branch_id"
                                              className="form-select"
                                              type="select"
                                              options={branchesList}
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-2"></div>
                                        <div className="col-lg-2 col-md-12">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="date"
                                              className="form-label"
                                            >
                                              Date{" "}
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="date"
                                              id="date"
                                              className="form-control"
                                              type="date"
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-2 col-md-12">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="time"
                                              className="form-label"
                                            >
                                              Time{" "}
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="time"
                                              id="time"
                                              className="form-control"
                                              type="time"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div className="row">
                                        <div className="col-lg-4 col-md-12">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="supplier_id"
                                              className="form-label"
                                            >
                                              Supplier{" "}
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="supplier_id"
                                              id="supplier_id"
                                              className="form-select"
                                              type="select"
                                              options={supplierList}
                                              onChange={(e) =>
                                                handleSupplierChange(
                                                  e,
                                                  setFieldValue
                                                )
                                              }
                                            />
                                          </div>
                                        </div>
                                        <div className="col-lg-2 col-md-12"></div>
                                        {values.currency != "" && (
                                          <div className="col-lg-2">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="currency"
                                                className="form-label"
                                              >
                                                Currency
                                              </label>
                                              <FormikField
                                                name="currency"
                                                id="currency"
                                                className="form-control"
                                                type="text"
                                                disabled
                                              />
                                            </div>
                                          </div>
                                        )}
                                        {values.currency != "" && (
                                          <div className="col-lg-2 col-md-12">
                                            <div className="mb-3">
                                              <label
                                                htmlFor="exchange_rate"
                                                className="form-label"
                                              >
                                                Exchange Rate ({shopCurrency})
                                                <span className="un-validation">
                                                  (*)
                                                </span>
                                              </label>
                                              <FormikField
                                                name="exchange_rate"
                                                id="exchange_rate"
                                                className="form-control"
                                                type="number"
                                                step="0.01"
                                                min="0.0"
                                                placeholder="Exchange Rate "
                                                value={values.exchange_rate}
                                              />
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                      <div className="row">
                                        <div className="col-lg-4 col-md-12">
                                          <div className="mb-3">
                                            <label
                                              htmlFor="invoice_type"
                                              className="form-label"
                                            >
                                              Invoice Type{" "}
                                              <span className="un-validation">
                                                (*)
                                              </span>
                                            </label>
                                            <FormikField
                                              name="invoice_type"
                                              id="invoice_type"
                                              className="form-select"
                                              type="select"
                                              options={invoiceTypeList}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      {values.exchange_rate != "" &&
                                        values.exchange_rate != null && (
                                          <>
                                            <hr></hr>
                                            <h4 className="card-title">
                                              Enter Item Details
                                            </h4>
                                            <div className="row">
                                              <div className="card-body">
                                                <FieldArray name="items">
                                                  {({ push, remove }) => (
                                                    <div className="row ">
                                                      <div className="row">
                                                        <div className="col-sm-3 ">
                                                          <label
                                                            htmlFor=""
                                                            className="form-label "
                                                          >
                                                            Item Description{" "}
                                                            <span className="un-validation">
                                                              (*)
                                                            </span>
                                                          </label>
                                                        </div>
                                                        <div className="col-2 align-center">
                                                          <label
                                                            htmlFor=""
                                                            className="form-label"
                                                          >
                                                            Unit Price (
                                                            {values.currency}){" "}
                                                            <span className="un-validation">
                                                              (*)
                                                            </span>
                                                          </label>
                                                        </div>
                                                        <div className="col-1 align-center">
                                                          <label
                                                            htmlFor=""
                                                            className="form-label"
                                                          >
                                                            Qty {""}
                                                            <span className="un-validation">
                                                              (*)
                                                            </span>
                                                          </label>
                                                        </div>
                                                        <div className="col-2 align-center">
                                                          <label
                                                            htmlFor=""
                                                            className="form-label "
                                                          >
                                                            Total Price (
                                                            {values.currency})
                                                          </label>
                                                        </div>
                                                        <div className="col-2 align-center">
                                                          <label
                                                            htmlFor=""
                                                            className="form-label "
                                                          >
                                                            Total Price (
                                                            {shopCurrency})
                                                          </label>
                                                        </div>
                                                        <div className="col-2"></div>
                                                      </div>
                                                      <hr></hr>
                                                      <div
                                                        id="fields"
                                                        className="my-4"
                                                      >
                                                        {values.items.map(
                                                          (item, index) => (
                                                            <div
                                                              className="row"
                                                              key={index}
                                                            >
                                                              <div className="col-3">
                                                                <div className="mb-3">
                                                                  <FormikField
                                                                    type="text"
                                                                    name={`items[${index}].description`}
                                                                    id={`items[${index}].description`}
                                                                    placeholder="Item Description *"
                                                                    autoComplete="off"
                                                                    className="form-control"
                                                                    value={
                                                                      values
                                                                        .items[
                                                                        index
                                                                      ]
                                                                        .description
                                                                    }
                                                                  />
                                                                </div>
                                                              </div>
                                                              <div className="col-2">
                                                                <div className="mb-3">
                                                                  <FormikField
                                                                    type="number"
                                                                    name={`items[${index}].unit_price`}
                                                                    id={`items[${index}].unit_price`}
                                                                    placeholder={`Unit Price in ${values.currency} *`}
                                                                    autoComplete="off"
                                                                    className="form-control"
                                                                    step="0.01"
                                                                    min="1"
                                                                    value={
                                                                      values
                                                                        .items[
                                                                        index
                                                                      ]
                                                                        .unit_price
                                                                    }
                                                                  />
                                                                </div>
                                                              </div>
                                                              <div className="col-1">
                                                                <div className="mb-3">
                                                                  <FormikField
                                                                    type="number"
                                                                    name={`items[${index}].quantity`}
                                                                    id={`items[${index}].quantity`}
                                                                    placeholder="Qty *"
                                                                    autoComplete="off"
                                                                    className="form-control"
                                                                    min="1"
                                                                    value={
                                                                      values
                                                                        .items[
                                                                        index
                                                                      ].quantity
                                                                    }
                                                                  />
                                                                </div>
                                                              </div>
                                                              <div className="col-2">
                                                                <div className="mb-3">
                                                                  <FormikField
                                                                    type="number"
                                                                    name={`items[${index}].total_price`}
                                                                    id={`items[${index}].total_price`}
                                                                    placeholder={`Total Price (${values.currency})`}
                                                                    autoComplete="off"
                                                                    className="form-control"
                                                                    step="0.01"
                                                                    readOnly={
                                                                      true
                                                                    }
                                                                    value={
                                                                      values
                                                                        .items[
                                                                        index
                                                                      ]
                                                                        .total_price
                                                                    }
                                                                  />
                                                                </div>
                                                              </div>
                                                              <div className="col-2">
                                                                <div className="mb-3">
                                                                  <FormikField
                                                                    type="number"
                                                                    name={`items[${index}].total_price_in_shop_currency`}
                                                                    id={`items[${index}].total_price_in_shop_currency`}
                                                                    placeholder={`Total Price (${shopCurrency})`}
                                                                    autoComplete="off"
                                                                    className="form-control"
                                                                    step="0.01"
                                                                    readOnly={
                                                                      true
                                                                    }
                                                                    value={
                                                                      values
                                                                        .items[
                                                                        index
                                                                      ]
                                                                        .total_price_in_shop_currency
                                                                    }
                                                                  />
                                                                </div>
                                                              </div>
                                                              <div className="col-2">
                                                                <div className="mb-3">
                                                                  {index >
                                                                    0 && (
                                                                    <button
                                                                      type="button"
                                                                      className="btn btn-danger fw-medium"
                                                                      onClick={() =>
                                                                        remove(
                                                                          index
                                                                        )
                                                                      }
                                                                    >
                                                                      <i className="ti ti-circle-minus fs-5 d-flex"></i>
                                                                    </button>
                                                                  )}
                                                                  {index ===
                                                                    values.items
                                                                      .length -
                                                                      1 && (
                                                                    <button
                                                                      type="button"
                                                                      className="btn btn-success fw-medium ms-1"
                                                                      onClick={() =>
                                                                        push({
                                                                          description:
                                                                            "",
                                                                          unit_price:
                                                                            "",
                                                                          quantity:
                                                                            "",
                                                                          total_price:
                                                                            "",
                                                                          total_price_in_shop_currency:
                                                                            "",
                                                                        })
                                                                      }
                                                                    >
                                                                      <i className="ti ti-circle-plus fs-5 d-flex"></i>
                                                                    </button>
                                                                  )}
                                                                </div>
                                                              </div>
                                                            </div>
                                                          )
                                                        )}
                                                      </div>
                                                      <hr></hr>
                                                      <div className="d-flex justify-content-end">                                                        
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="currency"
                                                              className="form-label"
                                                            >
                                                              Shipping Currency
                                                            </label>
                                                            <FormikField
                                                              name="shipping_currency"
                                                              id="shipping_currency"
                                                              className="form-select"
                                                              type="select"
                                                              options={
                                                                conversionCurrencyList
                                                              }
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ms-2">                                                          
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="shipping_charge"
                                                              className="form-label"
                                                            >
                                                              Shipping Charge
                                                              {values.shipping_currency
                                                                ? " (" +
                                                                  values.shipping_currency +
                                                                  ")"
                                                                : ""}
                                                            </label>
                                                            <FormikField
                                                              name="shipping_charge"
                                                              id="shipping_charge"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              placeholder={`Shipping Price in ${values.shipping_currency}`}
                                                            />
                                                          </div>
                                                        </div>
                                                      </div>
                                                      <div className="d-flex justify-content-end">
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="transaction_fee_currency"
                                                              className="form-label"
                                                            >
                                                              Transaction Fee
                                                              Currency
                                                            </label>
                                                            <FormikField
                                                              name="transaction_fee_currency"
                                                              id="transaction_fee_currency"
                                                              className="form-select"
                                                              type="select"
                                                              options={
                                                                conversionCurrencyList
                                                              }
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ms-2">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="transaction_fee"
                                                              className="form-label"
                                                            >
                                                              Transaction Fee
                                                              {values.transaction_fee_currency
                                                                ? " (" +
                                                                  values.transaction_fee_currency +
                                                                  ")"
                                                                : ""}
                                                            </label>
                                                            <FormikField
                                                              name="transaction_fee"
                                                              id="transaction_fee"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              placeholder={`Transaction Fee in ${values.transaction_fee_currency}`}
                                                            />
                                                          </div>
                                                        </div>
                                                      </div>
                                                      {/* <div className="d-flex justify-content-end">
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="other_expense_currency"
                                                              className="form-label"
                                                            >
                                                              Other Expense
                                                              Currency
                                                            </label>
                                                            <FormikField
                                                              name="other_expense_currency"
                                                              id="other_expense_currency"
                                                              className="form-select"
                                                              type="select"
                                                              options={
                                                                conversionCurrencyList
                                                              }
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ms-2">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="other_expense"
                                                              className="form-label"
                                                            >
                                                              Other Expense
                                                              {values.other_expense_currency
                                                                ? " (" +
                                                                  values.other_expense_currency +
                                                                  ")"
                                                                : ""}
                                                            </label>
                                                            <FormikField
                                                              name="other_expense"
                                                              id="other_expense"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              placeholder={`Other Expense in ${values.other_expense_currency}`}
                                                            />
                                                          </div>
                                                        </div>
                                                      </div> */}
                                                      <hr></hr>
                                                      <div className="d-flex justify-content-end">
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="sub_total"
                                                              className="form-label"
                                                            >
                                                              Sub Total (
                                                              {values.currency})
                                                            </label>
                                                            <FormikField
                                                              name="sub_total"
                                                              id="sub_total"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              readOnly={true}
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ms-2">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="sub_total_in_shop_currency"
                                                              className="form-label"
                                                            >
                                                              Sub Total (
                                                              {shopCurrency})
                                                            </label>
                                                            <FormikField
                                                              name="sub_total_in_shop_currency"
                                                              id="sub_total_in_shop_currency"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              readOnly={true}
                                                            />
                                                          </div>
                                                        </div>
                                                      </div>
                                                      <div className="d-flex justify-content-end">
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ms-1">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="shipping_charge_in_shop_currency"
                                                              className="form-label"
                                                            >
                                                              Shipping Charge (
                                                              {shopCurrency})
                                                            </label>
                                                            <FormikField
                                                              name="shipping_charge_in_shop_currency"
                                                              id="shipping_charge_in_shop_currency"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              readOnly={true}
                                                            />
                                                          </div>
                                                        </div>
                                                      </div>
                                                      <div className="d-flex justify-content-end">
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ms-1">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="transaction_fee_in_shop_currency"
                                                              className="form-label"
                                                            >
                                                              Transaction Fee (
                                                              {shopCurrency})
                                                            </label>
                                                            <FormikField
                                                              name="transaction_fee_in_shop_currency"
                                                              id="transaction_fee_in_shop_currency"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              readOnly={true}
                                                            />
                                                          </div>
                                                        </div>
                                                      </div>
                                                      {/* <div className="d-flex justify-content-end">
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ms-1">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="other_expense_in_shop_currency"
                                                              className="form-label"
                                                            >
                                                              Other Expense (
                                                              {shopCurrency})
                                                            </label>
                                                            <FormikField
                                                              name="other_expense_in_shop_currency"
                                                              id="other_expense_in_shop_currency"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              readOnly={true}
                                                            />
                                                          </div>
                                                        </div>
                                                      </div> */}
                                                      <hr></hr>
                                                      <div className="d-flex justify-content-end">
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="total_amount"
                                                              className="form-label"
                                                            >
                                                              Total Amount (
                                                              {values.currency})
                                                            </label>
                                                            <FormikField
                                                              name="total_amount"
                                                              id="total_amount"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              readOnly={true}
                                                            />
                                                          </div>
                                                        </div>
                                                        <div className="col-xl-2 col-lg-4 col-md-4 col-sm-12 ms-2">
                                                          <div className="mb-3">
                                                            <label
                                                              htmlFor="total_amount_in_shop_currency"
                                                              className="form-label"
                                                            >
                                                              Total Amount (
                                                              {shopCurrency})
                                                            </label>
                                                            <FormikField
                                                              name="total_amount_in_shop_currency"
                                                              id="total_amount_in_shop_currency"
                                                              className="form-control"
                                                              type="number"
                                                              step="0.01"
                                                              min="0"
                                                              readOnly={true}
                                                            />
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  )}
                                                </FieldArray>
                                              </div>
                                            </div>
                                            <div className="col-12">
                                              <div className="d-flex align-items-center justify-content-end mt-4 gap-6">
                                                <button
                                                  className="btn btn-primary"
                                                  type="submit"
                                                >
                                                  Create Invoice
                                                </button>
                                              </div>
                                            </div>
                                          </>
                                        )}
                                    </Form>
                                  </>
                                );
                              }}
                            </Formik>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
