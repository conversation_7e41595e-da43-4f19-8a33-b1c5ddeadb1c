import Swal from "sweetalert2";

/**
 * Custom hook for showing confirmation dialogs
 * @param {Object} options - Configuration options for the confirmation dialog
 * @param {string} options.title - Title of the confirmation dialog
 * @param {string} options.text - Text content of the confirmation dialog
 * @param {string} options.confirmButtonText - Text for the confirm button
 * @param {string} options.cancelButtonText - Text for the cancel button
 * @param {string} options.icon - Icon type (warning, error, success, info, question)
 * @returns {Function} showConfirm - Function to show the confirmation dialog
 */
const useConfirm = (options = {}) => {
  const defaultOptions = {
    title: "Are you sure?",
    text: "Please confirm your action.",
    icon: "question",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Confirm",
    cancelButtonText: "Cancel",
    reverseButtons: true,
    ...options,
  };

  /**
   * Show confirmation dialog and execute callback if confirmed
   * @param {Function} onConfirm - Callback function to execute when user confirms
   * @param {Function} onCancel - Optional callback function to execute when user cancels
   * @param {Object} successOptions - Options for success message
   * @returns {Promise} Promise that resolves with the result
   */
  const showConfirm = async (
    onConfirm,
    onCancel = null,
    successOptions = {}
  ) => {
    try {
      const result = await Swal.fire(defaultOptions);

      if (result.isConfirmed) {
        // User clicked confirm
        if (typeof onConfirm === "function") {
          await onConfirm();
        }

        // Show success message
        await Swal.fire({
          title: "Success!",
          text: "Action completed successfully.",
          icon: "success",
          confirmButtonColor: "#3085d6",
          timer: 2000,
          timerProgressBar: true,
          ...successOptions,
        });

        return { isConfirmed: true };
      } else if (result.isDismissed) {
        // User clicked cancel or dismissed
        if (typeof onCancel === "function") {
          await onCancel();
        }

        return { isConfirmed: false, isDismissed: true };
      }
    } catch (error) {
      console.error("Error in confirmation dialog:", error);

      // Show error message
      await Swal.fire({
        title: "Error!",
        text: "An error occurred while processing your request.",
        icon: "error",
        confirmButtonColor: "#d33",
      });

      return { isConfirmed: false, error: true };
    }
  };

  /**
   * Show simple confirmation dialog without callbacks
   * @param {Object} dynamicOptions - Options to override defaults for this specific call
   * @returns {Promise<boolean>} Promise that resolves to true if confirmed, false otherwise
   */
  const showSimpleConfirm = async (dynamicOptions = {}) => {
    try {
      const mergedOptions = { ...defaultOptions, ...dynamicOptions };
      const result = await Swal.fire(mergedOptions);
      return result.isConfirmed;
    } catch (error) {
      console.error("Error in simple confirmation dialog:", error);
      return false;
    }
  };

  return {
    showConfirm,
    showSimpleConfirm,
  };
};

/**
 * Quick confirmation with default settings
 * @param {Function} onConfirm - Callback function to execute when user confirms
 * @param {Object} customOptions - Custom options to override defaults
 * @param {Object} successOptions - Options for success message
 * @returns {Promise} Promise that resolves with the result
 */
export const quickConfirm = async (
  onConfirm,
  customOptions = {},
  successOptions = {}
) => {
  const { showConfirm } = useConfirm(customOptions);
  return await showConfirm(onConfirm, null, successOptions);
};

export default useConfirm;
