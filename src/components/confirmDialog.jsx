import useConfirm from "../hooks/useConfirm";

/**
 * Reusable Action Button Component with SweetAlert confirmation
 * @param {Object} props - Component props
 * @param {Function} props.onConfirm - Function to execute when action is confirmed
 * @param {Function} props.onCancel - Optional function to execute when action is cancelled
 * @param {string} props.itemName - Name of the item being acted upon (for display)
 * @param {string} props.className - CSS classes for the button
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {string} props.buttonText - Text to display on the button
 * @param {string} props.iconClass - Font Awesome icon class for the button
 * @param {Object} props.confirmOptions - Custom options for the confirmation dialog
 * @param {Object} props.successOptions - Custom options for the success message
 * @param {React.ReactNode} props.children - Optional children to render inside button
 */
const ConfirmDialog = ({
  onConfirm,
  onCancel = null,
  itemName = 'action',
  className = 'btn btn-primary btn-sm',
  disabled = false,
  buttonText = 'Action',
  iconClass = 'fas fa-check',
  confirmOptions = {},
  successOptions = {},
  children
}) => {
  const defaultConfirmOptions = {
    title: 'Confirm Action',
    text: `Do you want to proceed with this ${itemName}?`,
    icon: 'question',
    confirmButtonText: 'Yes, proceed!',
    ...confirmOptions
  };

  const { showConfirm } = useConfirm(defaultConfirmOptions);

  const handleClick = async () => {
    if (typeof onConfirm !== 'function') {
      console.error('onConfirm prop must be a function');
      return;
    }

    await showConfirm(
      async () => {
        // Execute the provided confirm function
        await onConfirm();
      },
      onCancel,
      successOptions
    );
  };

  return (
    <button
      type="button"
      className={className}
      onClick={handleClick}
      disabled={disabled}
    >
      {iconClass && !children && <i className={`${iconClass} me-1`}></i>}
      {children || buttonText}
    </button>
  );
};

export default ConfirmDialog;