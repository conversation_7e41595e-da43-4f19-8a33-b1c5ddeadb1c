// import { useConfirm } from '../hooks/useConfirm';

// /**
//  * Reusable Delete Button Component with SweetAlert confirmation
//  * @param {Object} props - Component props
//  * @param {Function} props.onDelete - Function to execute when delete is confirmed
//  * @param {string} props.itemName - Name of the item being deleted (for display)
//  * @param {string} props.className - CSS classes for the button
//  * @param {boolean} props.disabled - Whether the button is disabled
//  * @param {string} props.buttonText - Text to display on the button
//  * @param {Object} props.confirmOptions - Custom options for the confirmation dialog
//  */
// const DeleteConfirmButton = ({
//   onDelete,
//   itemName = 'item',
//   className = 'btn btn-danger btn-sm',
//   disabled = false,
//   buttonText = 'Delete',
//   confirmOptions = {}
// }) => {
//   const defaultConfirmOptions = {
//     title: 'Are you sure?',
//     text: `You won't be able to revert this ${itemName}!`,
//     icon: 'warning',
//     confirmButtonText: 'Yes, delete it!',
//     ...confirmOptions
//   };

//   const { showConfirmDelete } = useConfirm(defaultConfirmOptions);

//   const handleDeleteClick = async () => {
//     if (typeof onDelete !== 'function') {
//       console.error('onDelete prop must be a function');
//       return;
//     }

//     await showConfirmDelete(
//       async () => {
//         // Execute the delete function
//         await onDelete();
//       },
//       () => {
//         // Optional: Handle cancel action
//         console.log('Delete cancelled by user');
//       }
//     );
//   };

//   return (
//     <button
//       type="button"
//       className={className}
//       onClick={handleDeleteClick}
//       disabled={disabled}
//     >
//       <i className="fas fa-trash-alt me-1"></i>
//       {buttonText}
//     </button>
//   );
// };

// export default DeleteConfirmButton;
