import { useNavigate } from "react-router-dom";

export default function SideBarMenu() {
  const pathSegments = window.location.pathname.split("/").filter(Boolean); // Splits and removes empty strings
  const currentPath = pathSegments[0];
  const navigate = useNavigate();
  const handleNavigation = (route) => () => navigate(route);
  document.getElementById("main-wrapper").classList.add("show-sidebar");
  return (
    <>
      <div className="sidebarmenu">
        <div className="brand-logo d-flex align-items-center nav-logo">
          <a
            onClick={handleNavigation("/dashboard")}
            className="text-nowrap logo-img "
          >
            <img
              src="/src/assets/images/logos/logo.svg"
              alt="Logo"
              style={{ width: "80%" }}
            />
          </a>
        </div>
        <nav className="sidebar-nav " id="menu-right-mini-1" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Dashboards</span>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "dashboard" ? "active" : ""
                }`}
                onClick={handleNavigation("/dashboard")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:chart-line-duotone"></iconify-icon>
                <span className="hide-menu">Dashboard</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "account" ? "active" : ""
                }`}
                onClick={handleNavigation("/account")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:shield-user-line-duotone"></iconify-icon>
                <span className="hide-menu">Account Settings </span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "shop_profile" ? "active" : ""
                }`}
                onClick={handleNavigation("/shop_profile")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:shop-minimalistic-linear"></iconify-icon>
                <span className="hide-menu">Shop Profile</span>
              </a>
            </li>

            <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  ["branch_list", "createBranch", "editBranch"].includes(
                    currentPath
                  )
                    ? "active"
                    : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:map-point-linear"></iconify-icon>
                <span className="hide-menu">Branch Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  ["branch_list", "editBranch"].includes(currentPath)
                    ? "in"
                    : ""
                }`}
              >
                {/* <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createBranch"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/createBranch")}
                  >
                    <span className="icon-small"></span>
                    Create Branch
                  </a>
                </li> */}
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["branch_list", "editBranch"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/branch_list")}
                  >
                    <span className="icon-small"></span>
                    Branches
                  </a>
                </li>
              </ul>
            </li>
            <li className="sidebar-item">
              <a className={`sidebar-link  has-arrow ${
                  ["roles",
                      // "createRole",
                      "employees",
                      "createEmployee",
                      "editEmployee",].includes(
                    currentPath
                  )
                    ? "active"
                    : ""
                }`}  aria-expanded="false">
                <iconify-icon icon="solar:users-group-rounded-linear"></iconify-icon>
                <span className="hide-menu">Employees Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level 
                  ${
                    [
                      "roles",
                      // "createRole",
                      "employees",
                      "createEmployee",
                      "editEmployee",
                    ].includes(currentPath)
                      ? "in"
                      : ""
                  }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link 
                      ${
                        ["roles", "createRole"].includes(currentPath)
                          ? "active"
                          : ""
                      }`}
                    onClick={handleNavigation("/roles")}
                  >
                    <span className="icon-small"></span>
                    Employee Roles
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link 
                      ${["createEmployee"].includes(currentPath) ? "active" : ""}`}
                    onClick={handleNavigation("/createEmployee")}
                  >
                    <span className="icon-small"></span>
                    Create Employee
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link 
                      ${
                        ["employees", "editEmployee"].includes(currentPath)
                          ? "active"
                          : ""
                      }`}
                    onClick={handleNavigation("/employees")}
                  >
                    <span className="icon-small"></span>
                    Employees
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
        <nav className="sidebar-nav" id="menu-right-mini-2" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Product Master</span>
            </li>

            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "mainCategories" ? "active" : ""
                }`}
                onClick={handleNavigation("/mainCategories")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:folder-linear"></iconify-icon>
                <span className="hide-menu">Main Categories</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "subCategories" ? "active" : ""
                }`}
                onClick={handleNavigation("/subCategories")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:folder-open-linear"></iconify-icon>
                <span className="hide-menu">Sub Categories</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "childCategories" ? "active" : ""
                }`}
                onClick={handleNavigation("/childCategories")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:folder-path-connect-linear"></iconify-icon>
                <span className="hide-menu">Child Categories</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "attributes" ? "active" : ""
                }`}
                onClick={handleNavigation("/attributes")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:tag-linear"></iconify-icon>
                <span className="hide-menu">Attributes</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "attributesValues" ? "active" : ""
                }`}
                onClick={handleNavigation("/attributesValues")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:checklist-linear"></iconify-icon>
                <span className="hide-menu">Attributes Values</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "brands" ? "active" : ""
                }`}
                onClick={handleNavigation("/brands")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:bookmark-linear"></iconify-icon>
                <span className="hide-menu">Brands</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  ["products", "createProduct", "editProduct", "productDetails", "viewProduct", "viewProductDetail", "createProductDetail"].includes(currentPath) ? "active" : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:box-linear"></iconify-icon>
                <span className="hide-menu">Products Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  ["products", "createProduct", "editProduct", "viewProduct", "productDetails", "viewProductDetail", "createProductDetail"].includes(
                    currentPath
                  )
                    ? "in"
                    : ""
                }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createProduct"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/createProduct")}
                  >
                    <span className="icon-small"></span>
                    Create Product
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["products", "editProduct", "viewProduct"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/products")}
                  >
                    <span className="icon-small"></span>
                    All Products
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["productDetails", "viewProductDetail", "createProductDetail"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/productDetails")}
                  >
                    <span className="icon-small"></span>
                    All Product Details
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
        {/* <nav className="sidebar-nav" id="menu-right-mini-3" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Invoice Master</span>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  ["paymentToSupplier", "createPaymentToSupplier"].includes(currentPath) ? "active" : ""
                }`}
                onClick={handleNavigation("/paymentToSupplier")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:wallet-linear"></iconify-icon>
                <span className="hide-menu">Payment To Supplier</span>
              </a>
            </li>
          </ul>
        </nav> */}
        <nav className="sidebar-nav" id="menu-right-mini-3" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Inventory Master</span>
            </li>
            {/* <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  [
                    "inventory",
                    "createInventory",
                    "editInventory",
                    "outOfStockInventory",
                    "outOfStockQuantityInventory",
                    "minimumStockAlertQuantityInventory",
                    "inventoryHistory",
                  ].includes(currentPath)
                    ? "active"
                    : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:box-minimalistic-linear"></iconify-icon>
                <span className="hide-menu">Inventory Master</span>
              </a> */}
              {/* <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  [
                    "inventory",
                    "createInventory",
                    "editInventory",
                    "outOfStockInventory",
                    "outOfStockQuantityInventory",
                    "minimumStockAlertQuantityInventory",
                    "inventoryHistory",
                  ].includes(currentPath)
                    ? "in"
                    : ""
                }`} */}
              {/* > */}
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createInventory"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/createInventory")}
                  >
                    <iconify-icon icon="solar:box-minimalistic-linear"></iconify-icon>
                    <span className="icon-small"></span>
                    Add Inventory
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["inventory", "editInventory"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/inventory")}
                  >
                    <iconify-icon icon="solar:list-linear"></iconify-icon>
                    <span className="icon-small"></span>
                    All Inventory
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["outOfStockInventory"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/outOfStockInventory")}
                  >
                    <iconify-icon icon="solar:list-linear"></iconify-icon>
                    <span className="icon-small"></span>
                    Out Of Stock Status
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["outOfStockQuantityInventory"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/outOfStockQuantityInventory")}
                  >
                    <iconify-icon icon="solar:list-linear"></iconify-icon>
                    <span className="icon-small"></span>
                    Out Of Quantity
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["minimumStockAlertQuantityInventory"].includes(
                        currentPath
                      )
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation(
                      "/minimumStockAlertQuantityInventory"
                    )}
                  >
                    <iconify-icon icon="solar:list-linear"></iconify-icon>
                    <span className="icon-small"></span>
                    Min Stock Alert
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["inventoryHistory"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/inventoryHistory")}
                  >
                    <iconify-icon icon="solar:list-linear"></iconify-icon>
                    <span className="icon-small"></span>
                    Inventory History
                  </a>
                </li>
              {/* </ul> */}
            {/* </li> */}

            {/* <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  [
                    "paymentToSupplierInvoice",
                    "createPaymentToSupplierInvoice",
                    "editPaymentToSupplierInvoice",
                  ].includes(currentPath)
                    ? "active"
                    : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:user-check-linear"></iconify-icon>
                <span className="hide-menu">Invoice Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  [
                    "paymentToSupplierInvoice",
                    "createPaymentToSupplierInvoice",
                    "editPaymentToSupplierInvoice",
                  ].includes(currentPath)
                    ? "in"
                    : ""
                }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createPaymentToSupplierInvoice"].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation(
                      "/createPaymentToSupplierInvoice"
                    )}
                  >
                    <span className="icon-small"></span>
                    Create Invoices
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      [
                        "paymentToSupplierInvoice",
                        "editPaymentToSupplierInvoice",
                      ].includes(currentPath)
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/paymentToSupplierInvoice")}
                  >
                    <span className="icon-small"></span>
                    All Invoices
                  </a>
                </li>
              </ul>
            </li> */}
          </ul>
        </nav>
        <nav className="sidebar-nav" id="menu-right-mini-4" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Purchase Master</span>
            </li>
            {/* <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  [
                    "suppliers",
                    "createSupplier",
                    "editSupplier",
                    "viewSupplier",
                  ].includes(currentPath)
                    ? "active"
                    : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:user-check-linear"></iconify-icon>
                <span className="hide-menu">Supplier Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  [
                    "suppliers",
                    "createSupplier",
                    "editSupplier",
                    "viewSupplier",
                  ].includes(currentPath)
                    ? "in"
                    : ""
                }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createSupplier"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/createSupplier")}
                  >
                    <span className="icon-small"></span>
                    Add Supplier
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["suppliers", "editSupplier", "viewSupplier"].includes(
                        currentPath
                      )
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/suppliers")}
                  >
                    <span className="icon-small"></span>
                    All Suppliers
                  </a>
                </li>
              </ul>
            </li> */}

            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "createSupplier" ? "active" : ""
                }`}
                onClick={handleNavigation("/createSupplier")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:user-plus-linear"></iconify-icon>
                <span className="hide-menu"> Add Supplier </span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link 
                  ${
                    ["editSupplier", "suppliers", "viewSupplier"].includes(
                      currentPath
                    )
                      ? "active"
                      : ""
                  }`}
                onClick={handleNavigation("/suppliers")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:user-check-linear"></iconify-icon>
                <span className="hide-menu">All Suppliers </span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "createSupplierInvoice"
                    ? "active"
                    : ""
                }`}
                onClick={handleNavigation("/createSupplierInvoice")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:bill-linear"></iconify-icon>
                <span className="hide-menu"> Add Supplier Invoice </span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link 
                  ${
                    [
                      "supplierInvoice",
                      "editSupplierInvoice",
                      "previewSupplierInvoice"
                    ].includes(currentPath)
                      ? "active"
                      : ""
                  }`}
                onClick={handleNavigation("/supplierInvoice")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:bill-list-linear"></iconify-icon>
                <span className="hide-menu">All Supplier Invoices </span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "createPurchaseInvoice"
                    ? "active"
                    : ""
                }`}
                onClick={handleNavigation("/createPurchaseInvoice")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:bill-check-linear"></iconify-icon>
                <span className="hide-menu"> Add Purchase Invoice </span>
              </a>
            </li>

            <li className="sidebar-item">
              <a
                className={`sidebar-link 
                  ${
                    [
                      "purchaseInvoice",
                      "editPurchaseInvoice",
                      "previewPurchaseInvoice"
                    ].includes(currentPath)
                      ? "active"
                      : ""
                  }`}
                onClick={handleNavigation("/purchaseInvoice")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:bill-list-linear"></iconify-icon>
                <span className="hide-menu">All Purchase Invoices </span>
              </a>
            </li>
          </ul>
        </nav>
        <nav className="sidebar-nav" id="menu-right-mini-5" data-simplebar>
          <ul className="sidebar-menu" id="sidebarnav">
            <li className="nav-small-cap">
              <span className="hide-menu">Customer Master</span>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link has-arrow ${
                  [
                    "customers",
                    "createCustomer",
                    "editCustomer",
                    "viewCustomer",
                  ].includes(currentPath)
                    ? "active"
                    : ""
                }`}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:user-linear"></iconify-icon>
                <span className="hide-menu">Customer Master</span>
              </a>
              <ul
                aria-expanded="false"
                className={`collapse first-level ${
                  [
                    "customers",
                    "createCustomer",
                    "editCustomer",
                    "viewCustomer",
                  ].includes(currentPath)
                    ? "in"
                    : ""
                }`}
              >
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["createCustomer"].includes(currentPath) ? "active" : ""
                    }`}
                    onClick={handleNavigation("/createCustomer")}
                  >
                    <span className="icon-small"></span>
                    Add Customer
                  </a>
                </li>
                <li className="sidebar-item">
                  <a
                    className={`sidebar-link ${
                      ["customers", "editCustomer", "viewCustomer"].includes(
                        currentPath
                      )
                        ? "active"
                        : ""
                    }`}
                    onClick={handleNavigation("/customers")}
                  >
                    <span className="icon-small"></span>
                    All Customers
                  </a>
                </li>
              </ul>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "customerContacts" ? "active" : ""
                }`}
                onClick={handleNavigation("/customerContacts")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:phone-linear"></iconify-icon>
                <span className="hide-menu">Contacts</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "customerAttachments" ? "active" : ""
                }`}
                onClick={handleNavigation("/customerAttachments")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:paperclip-linear"></iconify-icon>
                <span className="hide-menu">Attachments</span>
              </a>
            </li>
            <li className="sidebar-item">
              <a
                className={`sidebar-link ${
                  currentPath === "customerNotes" ? "active" : ""
                }`}
                onClick={handleNavigation("/customerNotes")}
                aria-expanded="false"
              >
                <iconify-icon icon="solar:notes-linear"></iconify-icon>
                <span className="hide-menu">Notes</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </>
  );
}
